<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendas - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Vendas</h1>
                <p class="text-muted">Histórico de vendas realizadas</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('venda_nova') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Nova Venda
                </a>
            </div>
        </div>

        <!-- Lista de Vendas -->
        <div class="card">
            <div class="card-body">
                {% if vendas %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Cliente</th>
                                    <th>Vendedor</th>
                                    <th>Total</th>
                                    <th>Data</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for venda in vendas %}
                                <tr>
                                    <td>#{{ venda.id }}</td>
                                    <td>
                                        <strong>{{ venda.cliente_nome or 'Cliente não informado' }}</strong>
                                    </td>
                                    <td>{{ venda.vendedor_nome or 'N/A' }}</td>
                                    <td>
                                        <span class="fw-bold text-success">
                                            R$ {{ "%.2f"|format(venda.total_final) }}
                                        </span>
                                    </td>
                                    <td>{{ venda.data_venda }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('venda_sucesso', venda_id=venda.id) }}" class="btn btn-outline-primary" title="Ver Detalhes">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-info" title="Imprimir" onclick="imprimirVenda({{ venda.id }})">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <a href="{{ url_for('venda_pdf', venda_id=venda.id) }}" target="_blank" class="btn btn-outline-secondary" title="PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                            {% if session.user_tipo in ['admin', 'gerente'] %}
                                            <button class="btn btn-outline-danger" title="Cancelar Venda" onclick="confirmarCancelamento({{ venda.id }})">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhuma venda registrada</h5>
                        <p class="text-muted">Clique em "Nova Venda" para começar</p>
                        <a href="{{ url_for('venda_nova') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Registrar Primeira Venda
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação de Cancelamento -->
    <div class="modal fade" id="modalCancelar" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Cancelamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja cancelar a venda <strong id="vendaNumero"></strong>?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>Atenção:</strong> Esta ação irá:
                        <ul class="mb-0 mt-2">
                            <li>Retornar os produtos ao estoque</li>
                            <li>Marcar a venda como cancelada</li>
                            <li>Esta ação não pode ser desfeita</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Não, manter venda</button>
                    <form id="formCancelar" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">Sim, cancelar venda</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function imprimirVenda(vendaId) {
            // Abrir página de impressão em nova janela
            const printWindow = window.open(`/vendas/pdf/${vendaId}`, '_blank');
            printWindow.onload = function() {
                printWindow.print();
            };
        }

        function confirmarCancelamento(vendaId) {
            document.getElementById('vendaNumero').textContent = `#${vendaId}`;
            document.getElementById('formCancelar').action = `/vendas/cancelar/${vendaId}`;
            new bootstrap.Modal(document.getElementById('modalCancelar')).show();
        }
    </script>
</body>
</html>
