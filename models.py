"""
Modelos de dados do sistema Saúde Flex
"""

from database import db
import bcrypt
import json
from datetime import datetime

class Usuario:
    def __init__(self, id=None, nome=None, email=None, senha=None, tipo=None, ativo=True):
        self.id = id
        self.nome = nome
        self.email = email
        self.senha = senha
        self.tipo = tipo
        self.ativo = ativo

    @staticmethod
    def criar(nome, email, senha, tipo):
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            senha_hash = bcrypt.hashpw(senha.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            cursor.execute('''
                INSERT INTO usuarios (nome, email, senha, tipo)
                VALUES (?, ?, ?, ?)
            ''', (nome, email, senha_hash, tipo))
            user_id = cursor.lastrowid
            conn.commit()
            return user_id
        finally:
            conn.close()

    @staticmethod
    def buscar_por_email(email):
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT * FROM usuarios WHERE email = ? AND ativo = 1', (email,))
            row = cursor.fetchone()
            if row:
                return Usuario(
                    id=row['id'],
                    nome=row['nome'],
                    email=row['email'],
                    senha=row['senha'],
                    tipo=row['tipo'],
                    ativo=bool(row['ativo'])
                )
            return None
        finally:
            conn.close()

    @staticmethod
    def verificar_senha(senha, hash_senha):
        return bcrypt.checkpw(senha.encode('utf-8'), hash_senha.encode('utf-8'))

    @staticmethod
    def listar():
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT * FROM usuarios WHERE ativo = 1 ORDER BY nome')
            usuarios = cursor.fetchall()
            return [dict(row) for row in usuarios]
        finally:
            conn.close()

class Categoria:
    @staticmethod
    def criar(nome, descricao=None):
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('INSERT INTO categorias (nome, descricao) VALUES (?, ?)', (nome, descricao))
            categoria_id = cursor.lastrowid
            conn.commit()
            return categoria_id
        finally:
            conn.close()

    @staticmethod
    def listar():
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
            categorias = cursor.fetchall()
            return [dict(row) for row in categorias]
        finally:
            conn.close()

class Produto:
    @staticmethod
    def calcular_preco_venda(preco_custo, lucro_desejado, tipo_lucro):
        if tipo_lucro == 'percentual':
            return preco_custo * (1 + lucro_desejado / 100)
        else:
            return preco_custo + lucro_desejado

    @staticmethod
    def criar(nome, descricao, preco_custo, lucro_desejado, tipo_lucro, categoria_id=None, estoque_atual=0, estoque_minimo=5, foto=None):
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            preco_venda = Produto.calcular_preco_venda(preco_custo, lucro_desejado, tipo_lucro)
            cursor.execute('''
                INSERT INTO produtos (nome, descricao, preco_custo, lucro_desejado, tipo_lucro,
                                    preco_venda, categoria_id, estoque_atual, estoque_minimo, foto)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (nome, descricao, preco_custo, lucro_desejado, tipo_lucro,
                  preco_venda, categoria_id, estoque_atual, estoque_minimo, foto))
            produto_id = cursor.lastrowid
            conn.commit()
            return produto_id
        finally:
            conn.close()

    @staticmethod
    def listar():
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                SELECT p.*, c.nome as categoria_nome 
                FROM produtos p 
                LEFT JOIN categorias c ON p.categoria_id = c.id 
                WHERE p.ativo = 1 
                ORDER BY p.nome
            ''')
            produtos = cursor.fetchall()
            return [dict(row) for row in produtos]
        finally:
            conn.close()

class Cliente:
    @staticmethod
    def criar(nome, telefone, email=None, cpf=None, logradouro=None, numero=None,
              bairro=None, cidade=None, uf=None, cep=None, data_nascimento=None, observacoes=None):
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO clientes (nome, telefone, email, cpf, logradouro, numero, 
                                    bairro, cidade, uf, cep, data_nascimento, observacoes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (nome, telefone, email, cpf, logradouro, numero, 
                  bairro, cidade, uf, cep, data_nascimento, observacoes))
            cliente_id = cursor.lastrowid
            conn.commit()
            return cliente_id
        finally:
            conn.close()

    @staticmethod
    def listar():
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('SELECT * FROM clientes WHERE ativo = 1 ORDER BY nome')
            clientes = cursor.fetchall()
            return [dict(row) for row in clientes]
        finally:
            conn.close()

class Agendamento:
    @staticmethod
    def criar(cliente_id, produto_id, vendedor_id, data_agendamento, observacoes=None):
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO agendamentos (cliente_id, produto_id, vendedor_id, data_agendamento, observacoes)
                VALUES (?, ?, ?, ?, ?)
            ''', (cliente_id, produto_id, vendedor_id, data_agendamento, observacoes))
            agendamento_id = cursor.lastrowid
            conn.commit()
            return agendamento_id
        finally:
            conn.close()

    @staticmethod
    def listar():
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                SELECT a.*, c.nome as cliente_nome, p.nome as produto_nome, u.nome as vendedor_nome
                FROM agendamentos a
                JOIN clientes c ON a.cliente_id = c.id
                JOIN produtos p ON a.produto_id = p.id
                JOIN usuarios u ON a.vendedor_id = u.id
                ORDER BY a.data_agendamento DESC
            ''')
            agendamentos = cursor.fetchall()
            return [dict(row) for row in agendamentos]
        finally:
            conn.close()

class Venda:
    @staticmethod
    def criar(cliente_id, vendedor_id, itens, desconto_total=0, tipo_desconto_total='valor', observacoes=None):
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            subtotal = sum(item['quantidade'] * item['preco_unitario'] - item.get('desconto_item', 0) for item in itens)
            if tipo_desconto_total == 'percentual':
                desconto_aplicado = subtotal * (desconto_total / 100)
            else:
                desconto_aplicado = desconto_total
            total_final = subtotal - desconto_aplicado
            
            cursor.execute('''
                INSERT INTO vendas (cliente_id, vendedor_id, subtotal, desconto_total, 
                                  tipo_desconto_total, total_final, observacoes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (cliente_id, vendedor_id, subtotal, desconto_aplicado, 
                  tipo_desconto_total, total_final, observacoes))
            venda_id = cursor.lastrowid
            
            for item in itens:
                subtotal_item = item['quantidade'] * item['preco_unitario'] - item.get('desconto_item', 0)
                cursor.execute('''
                    INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario,
                                           desconto_item, tipo_desconto_item, subtotal_item)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (venda_id, item['produto_id'], item['quantidade'], item['preco_unitario'],
                      item.get('desconto_item', 0), item.get('tipo_desconto_item', 'valor'), subtotal_item))
            
            conn.commit()
            return venda_id
        finally:
            conn.close()

    @staticmethod
    def listar():
        conn = db.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
                FROM vendas v
                LEFT JOIN clientes c ON v.cliente_id = c.id
                JOIN usuarios u ON v.vendedor_id = u.id
                ORDER BY v.data_venda DESC
            ''')
            vendas = cursor.fetchall()
            return [dict(row) for row in vendas]
        finally:
            conn.close()
