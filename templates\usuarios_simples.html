<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Usuários - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .badge-admin {
            background-color: #dc3545;
        }
        .badge-gerente {
            background-color: #fd7e14;
        }
        .badge-vendedor {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('produtos') }}">
                            <i class="fas fa-box me-1"></i>Produtos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('clientes') }}">
                            <i class="fas fa-users me-1"></i>Clientes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('vendas') }}">
                            <i class="fas fa-shopping-cart me-1"></i>Vendas
                        </a>
                    </li>
                    {% if session.user_tipo == 'admin' %}
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('usuarios') }}">
                            <i class="fas fa-user-cog me-1"></i>Usuários
                        </a>
                    </li>
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ session.user_nome or 'Usuário' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i>Sair
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Usuários</h1>
                <p class="text-muted">Gerenciamento de usuários do sistema</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('usuario_novo') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Novo Usuário
                </a>
            </div>
        </div>

        <!-- Lista de Usuários -->
        <div class="card">
            <div class="card-body">
                {% if usuarios %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Email</th>
                                    <th>Tipo</th>
                                    <th>Status</th>
                                    <th>Data Cadastro</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for usuario in usuarios %}
                                <tr>
                                    <td>#{{ usuario.id }}</td>
                                    <td>
                                        <strong>{{ usuario.nome }}</strong>
                                        {% if usuario.id == session.user_id %}
                                            <span class="badge bg-info ms-1">Você</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ usuario.email }}</td>
                                    <td>
                                        {% if usuario.tipo == 'admin' %}
                                            <span class="badge badge-admin">
                                                <i class="fas fa-crown me-1"></i>Administrador
                                            </span>
                                        {% elif usuario.tipo == 'gerente' %}
                                            <span class="badge badge-gerente">
                                                <i class="fas fa-user-tie me-1"></i>Gerente
                                            </span>
                                        {% else %}
                                            <span class="badge badge-vendedor">
                                                <i class="fas fa-user me-1"></i>Vendedor
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if usuario.ativo %}
                                            <span class="badge bg-success">Ativo</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inativo</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ usuario.data_criacao }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% if usuario.id != session.user_id %}
                                            <button class="btn btn-outline-warning" title="Alterar Status">
                                                <i class="fas fa-toggle-on"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="Excluir">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum usuário cadastrado</h5>
                        <p class="text-muted">Clique em "Novo Usuário" para começar</p>
                        <a href="{{ url_for('usuario_novo') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Cadastrar Primeiro Usuário
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Informações sobre Permissões -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>Informações sobre Tipos de Usuário
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-danger">
                                    <i class="fas fa-crown me-1"></i>Administrador
                                </h6>
                                <ul class="small text-muted">
                                    <li>Acesso total ao sistema</li>
                                    <li>Gerencia usuários</li>
                                    <li>Gerencia produtos, vendas e agendamentos</li>
                                    <li>Acesso a todos os relatórios</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-warning">
                                    <i class="fas fa-user-tie me-1"></i>Gerente
                                </h6>
                                <ul class="small text-muted">
                                    <li>Gerencia produtos e categorias</li>
                                    <li>Gerencia vendas e agendamentos</li>
                                    <li>Gerencia clientes</li>
                                    <li>Acesso a relatórios operacionais</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-success">
                                    <i class="fas fa-user me-1"></i>Vendedor
                                </h6>
                                <ul class="small text-muted">
                                    <li>Realizar vendas</li>
                                    <li>Criar agendamentos</li>
                                    <li>Cadastrar novos clientes</li>
                                    <li>Visualizar próprias vendas</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
