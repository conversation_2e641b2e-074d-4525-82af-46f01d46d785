<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venda Realizada - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .success-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }
        .success-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        .btn-print {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            color: white;
        }
        .btn-print:hover {
            background: linear-gradient(135deg, #5a2d91 0%, #c02456 100%);
            color: white;
        }
        .btn-pdf {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            border: none;
            color: white;
        }
        .btn-pdf:hover {
            background: linear-gradient(135deg, #a71e2a 0%, #e8590c 100%);
            color: white;
        }
        .total-destaque {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Card de Sucesso -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="success-header">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2 class="mb-0">Venda Realizada com Sucesso!</h2>
                        <p class="mb-0">Venda #{{ venda.id }} - {{ venda.data_venda }}</p>
                    </div>
                    
                    <div class="card-body">
                        <!-- Informações da Venda -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h6><i class="fas fa-user me-2"></i>Cliente</h6>
                                <p class="mb-1">{{ venda.cliente_nome or 'Cliente não informado' }}</p>
                                {% if venda.cliente_telefone %}
                                <p class="text-muted small">{{ venda.cliente_telefone }}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-user-tie me-2"></i>Vendedor</h6>
                                <p class="mb-1">{{ venda.vendedor_nome }}</p>
                                <p class="text-muted small">{{ venda.data_venda }}</p>
                            </div>
                        </div>

                        <!-- Itens da Venda -->
                        <h6><i class="fas fa-shopping-cart me-2"></i>Itens da Venda</h6>
                        <div class="table-responsive mb-4">
                            <table class="table table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>Produto</th>
                                        <th>Qtd</th>
                                        <th>Preço Unit.</th>
                                        <th>Desconto</th>
                                        <th>Subtotal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in itens %}
                                    <tr>
                                        <td>{{ item.produto_nome }}</td>
                                        <td>{{ item.quantidade }}</td>
                                        <td>R$ {{ "%.2f"|format(item.preco_unitario) }}</td>
                                        <td>R$ {{ "%.2f"|format(item.desconto_item) }}</td>
                                        <td>R$ {{ "%.2f"|format(item.subtotal_item) }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Totais -->
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Subtotal:</span>
                                    <span>R$ {{ "%.2f"|format(venda.subtotal) }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Desconto Total:</span>
                                    <span>R$ {{ "%.2f"|format(venda.desconto_total) }}</span>
                                </div>
                                <hr>
                                <div class="total-destaque">
                                    Total Final: R$ {{ "%.2f"|format(venda.total_final) }}
                                </div>
                            </div>
                        </div>

                        {% if venda.observacoes %}
                        <div class="mt-4">
                            <h6><i class="fas fa-sticky-note me-2"></i>Observações</h6>
                            <p class="text-muted">{{ venda.observacoes }}</p>
                        </div>
                        {% endif %}

                        <!-- Botões de Ação -->
                        <div class="d-flex justify-content-center gap-3 mt-4">
                            <button class="btn btn-print btn-lg" onclick="imprimirVenda()">
                                <i class="fas fa-print me-2"></i>Imprimir Venda
                            </button>
                            <a href="{{ url_for('venda_pdf', venda_id=venda.id) }}" target="_blank" class="btn btn-pdf btn-lg">
                                <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                            </a>
                            <a href="{{ url_for('venda_nova') }}" class="btn btn-success btn-lg">
                                <i class="fas fa-plus me-2"></i>Nova Venda
                            </a>
                            <a href="{{ url_for('vendas') }}" class="btn btn-secondary btn-lg">
                                <i class="fas fa-list me-2"></i>Ver Todas as Vendas
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function imprimirVenda() {
            // Abrir página de impressão em nova janela
            const printWindow = window.open('{{ url_for("venda_pdf", venda_id=venda.id) }}', '_blank');
            printWindow.onload = function() {
                printWindow.print();
            };
        }
    </script>
</body>
</html>
