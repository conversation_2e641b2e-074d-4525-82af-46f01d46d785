<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Estoque Baixo - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .estoque-critico {
            background-color: #fff5f5;
            border-left: 4px solid #dc3545;
        }
        .estoque-baixo {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
        }
        .btn-print {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            color: white;
        }
        .btn-print:hover {
            background: linear-gradient(135deg, #5a2d91 0%, #c02456 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Relatório de Estoque Baixo
                </h1>
                <p class="text-muted">Produtos que precisam de reposição urgente</p>
            </div>
            <div class="col-auto">
                <button class="btn btn-print me-2" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>Imprimir
                </button>
                <a href="{{ url_for('relatorios') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar
                </a>
            </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="categoria" class="form-label">Categoria</label>
                        <select class="form-select" id="categoria" name="categoria">
                            <option value="">Todas as categorias</option>
                            {% for categoria in categorias %}
                            <option value="{{ categoria.id }}">{{ categoria.nome }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="criticidade" class="form-label">Criticidade</label>
                        <select class="form-select" id="criticidade" name="criticidade">
                            <option value="">Todos os níveis</option>
                            <option value="critico">Crítico (estoque zerado)</option>
                            <option value="baixo">Baixo (abaixo do mínimo)</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i>Filtrar
                        </button>
                        <a href="{{ url_for('relatorio_estoque_baixo') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Limpar
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Resumo -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-danger">{{ produtos|length }}</h3>
                        <p class="text-muted mb-0">Produtos com Estoque Baixo</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning">{{ produtos|selectattr('estoque_atual', 'equalto', 0)|list|length }}</h3>
                        <p class="text-muted mb-0">Produtos em Falta</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info">{{ (produtos|map(attribute='estoque_minimo')|sum) - (produtos|map(attribute='estoque_atual')|sum) }}</h3>
                        <p class="text-muted mb-0">Unidades a Repor</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Produtos -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Produtos com Estoque Baixo
                </h5>
            </div>
            <div class="card-body">
                {% if produtos %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Produto</th>
                                    <th>Categoria</th>
                                    <th>Estoque Atual</th>
                                    <th>Estoque Mínimo</th>
                                    <th>Diferença</th>
                                    <th>Última Venda</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for produto in produtos %}
                                <tr class="{{ 'estoque-critico' if produto.estoque_atual == 0 else 'estoque-baixo' }}">
                                    <td>
                                        <strong>{{ produto.nome }}</strong>
                                        {% if produto.descricao %}
                                        <br><small class="text-muted">{{ produto.descricao }}</small>
                                        {% endif %}
                                    </td>
                                    <td>{{ produto.categoria_nome or '-' }}</td>
                                    <td>
                                        <span class="badge {{ 'bg-danger' if produto.estoque_atual == 0 else 'bg-warning' }}">
                                            {{ produto.estoque_atual }}
                                        </span>
                                    </td>
                                    <td>{{ produto.estoque_minimo }}</td>
                                    <td>
                                        <span class="text-danger fw-bold">
                                            {{ produto.estoque_atual - produto.estoque_minimo }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if produto.ultima_venda %}
                                            {{ produto.ultima_venda }}
                                        {% else %}
                                            <span class="text-muted">Nunca vendido</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if produto.estoque_atual == 0 %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times me-1"></i>Em Falta
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>Baixo
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">Parabéns!</h5>
                        <p class="text-muted">Todos os produtos estão com estoque adequado.</p>
                        <a href="{{ url_for('produtos') }}" class="btn btn-primary">
                            <i class="fas fa-box me-1"></i>Ver Produtos
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Recomendações -->
        {% if produtos %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Recomendações
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">🚨 Ações Urgentes</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-danger me-2"></i>Repor produtos em falta imediatamente</li>
                            <li><i class="fas fa-arrow-right text-warning me-2"></i>Contatar fornecedores dos produtos críticos</li>
                            <li><i class="fas fa-arrow-right text-info me-2"></i>Revisar estoque mínimo dos produtos frequentes</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">📊 Análise</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-chart-line text-success me-2"></i>Produtos nunca vendidos podem ter estoque reduzido</li>
                            <li><i class="fas fa-chart-bar text-info me-2"></i>Produtos com vendas frequentes precisam de mais estoque</li>
                            <li><i class="fas fa-cog text-secondary me-2"></i>Configure alertas automáticos para reposição</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
