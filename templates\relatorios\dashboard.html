{% extends "base.html" %}

{% block title %}Relatórios - Saúde Flex{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-bar me-2 text-primary"></i>
                Relatórios
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="exportarRelatorios()">
                    <i class="fas fa-download me-2"></i>
                    Exportar Todos
                </button>
                <button type="button" class="btn btn-outline-info" onclick="agendarRelatorio()">
                    <i class="fas fa-clock me-2"></i>
                    Agendar Envio
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cards de Relatórios -->
<div class="row">
    <!-- Relatório de Estoque Baixo -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Produtos com Estoque Baixo
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            5 produtos
                        </div>
                        <div class="text-xs text-muted">
                            Produtos abaixo do estoque mínimo
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('relatorio_estoque_baixo') }}" class="btn btn-warning btn-sm w-100">
                        <i class="fas fa-eye me-2"></i>
                        Ver Relatório
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Relatório de Vendas por Período -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Vendas por Período
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            R$ 15.450,00
                        </div>
                        <div class="text-xs text-muted">
                            Faturamento dos últimos 30 dias
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('relatorio_vendas_periodo') }}" class="btn btn-success btn-sm w-100">
                        <i class="fas fa-eye me-2"></i>
                        Ver Relatório
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Relatório de Produtos Mais Vendidos -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Produtos Mais Vendidos
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            Top 10
                        </div>
                        <div class="text-xs text-muted">
                            Ranking de produtos por vendas
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-trophy fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('relatorio_produtos_mais_vendidos') }}" class="btn btn-info btn-sm w-100">
                        <i class="fas fa-eye me-2"></i>
                        Ver Relatório
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Relatório de Vendas por Vendedor -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Vendas por Vendedor
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            3 vendedores
                        </div>
                        <div class="text-xs text-muted">
                            Performance da equipe de vendas
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-tie fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('relatorio_vendas_por_vendedor') }}" class="btn btn-primary btn-sm w-100">
                        <i class="fas fa-eye me-2"></i>
                        Ver Relatório
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Relatório de Lucro -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Relatório de Lucro
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            R$ 4.850,00
                        </div>
                        <div class="text-xs text-muted">
                            Lucro líquido dos últimos 30 dias
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('relatorio_lucro') }}" class="btn btn-success btn-sm w-100">
                        <i class="fas fa-eye me-2"></i>
                        Ver Relatório
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Relatório de Agendamentos -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Agendamentos
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            12 agendados
                        </div>
                        <div class="text-xs text-muted">
                            Agendamentos por período
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-check fa-2x text-gray-300"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('relatorio_agendamentos_periodo') }}" class="btn btn-info btn-sm w-100">
                        <i class="fas fa-eye me-2"></i>
                        Ver Relatório
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Gráficos Resumo -->
<div class="row">
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-area me-2"></i>
                    Vendas dos Últimos 7 Dias
                </h6>
            </div>
            <div class="card-body">
                <canvas id="graficoVendas" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie me-2"></i>
                    Produtos por Categoria
                </h6>
            </div>
            <div class="card-body">
                <canvas id="graficoCategorias" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Ações Rápidas -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>
                    Ações Rápidas
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-outline-warning w-100" onclick="verificarEstoqueBaixo()">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Verificar Estoque
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-outline-success w-100" onclick="gerarRelatorioVendas()">
                            <i class="fas fa-chart-bar me-2"></i>
                            Relatório de Vendas
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-outline-info w-100" onclick="analisarPerformance()">
                            <i class="fas fa-analytics me-2"></i>
                            Análise de Performance
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-outline-primary w-100" onclick="exportarDados()">
                            <i class="fas fa-download me-2"></i>
                            Exportar Dados
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Gráfico de Vendas
const ctxVendas = document.getElementById('graficoVendas').getContext('2d');
const graficoVendas = new Chart(ctxVendas, {
    type: 'line',
    data: {
        labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
        datasets: [{
            label: 'Vendas (R$)',
            data: [1200, 1900, 800, 1500, 2000, 1800, 900],
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            tension: 0.3
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'R$ ' + value.toLocaleString('pt-BR');
                    }
                }
            }
        }
    }
});

// Gráfico de Categorias
const ctxCategorias = document.getElementById('graficoCategorias').getContext('2d');
const graficoCategorias = new Chart(ctxCategorias, {
    type: 'doughnut',
    data: {
        labels: ['Bem-estar', 'Saúde', 'Beleza', 'Suplementos', 'Equipamentos'],
        datasets: [{
            data: [30, 25, 20, 15, 10],
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e',
                '#e74a3b'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Funções das ações rápidas
function verificarEstoqueBaixo() {
    window.location.href = '/relatorios/estoque-baixo';
}

function gerarRelatorioVendas() {
    window.location.href = '/relatorios/vendas-periodo';
}

function analisarPerformance() {
    SaudeFlex.notify.info('Análise de performance será implementada em breve!');
}

function exportarDados() {
    SaudeFlex.notify.info('Exportação de dados será implementada em breve!');
}

function exportarRelatorios() {
    SaudeFlex.notify.info('Exportação de relatórios será implementada em breve!');
}

function agendarRelatorio() {
    SaudeFlex.notify.info('Agendamento de relatórios será implementado em breve!');
}
</script>
{% endblock %}
