<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ 'Editar' if categoria else 'Nova' }} Categoria - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">{{ 'Editar' if categoria else 'Nova' }} Categoria</h1>
                <p class="text-muted">{{ 'Editar informações da' if categoria else 'Cadastrar nova' }} categoria</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('categorias') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar
                </a>
            </div>
        </div>

        <!-- Formulário -->
        <div class="row">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tag me-2"></i>Informações da Categoria
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="mb-3">
                                <label for="nome" class="form-label">Nome da Categoria *</label>
                                <input type="text" class="form-control" id="nome" name="nome" 
                                       value="{{ categoria.nome if categoria else '' }}" required>
                                <div class="form-text">Nome único para identificar a categoria</div>
                            </div>

                            <div class="mb-3">
                                <label for="descricao" class="form-label">Descrição</label>
                                <textarea class="form-control" id="descricao" name="descricao" rows="4"
                                          placeholder="Descreva o tipo de produtos desta categoria">{{ categoria.descricao if categoria else '' }}</textarea>
                                <div class="form-text">Descrição opcional para a categoria</div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <a href="{{ url_for('categorias') }}" class="btn btn-secondary me-2">Cancelar</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>{{ 'Atualizar' if categoria else 'Salvar' }} Categoria
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>Dicas
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong class="text-primary">📝 Nome da Categoria</strong>
                            <p class="small text-muted mb-2">Escolha um nome claro e único</p>
                            <ul class="small text-muted">
                                <li>Suplementos</li>
                                <li>Proteínas</li>
                                <li>Vitaminas</li>
                                <li>Cosméticos</li>
                            </ul>
                        </div>
                        
                        <div class="mb-3">
                            <strong class="text-success">📋 Descrição</strong>
                            <p class="small text-muted mb-2">Ajuda a identificar o tipo de produtos</p>
                            <ul class="small text-muted">
                                <li>Descreva o propósito da categoria</li>
                                <li>Mencione tipos de produtos incluídos</li>
                                <li>Use linguagem clara e objetiva</li>
                            </ul>
                        </div>
                        
                        {% if categoria %}
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-1"></i>
                            <strong>Editando categoria:</strong> As alterações afetarão todos os produtos vinculados a esta categoria.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
