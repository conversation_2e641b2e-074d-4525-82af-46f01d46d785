"""
Sistema de Gestão Saúde Flex
Aplicação Flask para gestão de vendas, agendamentos e relatórios
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory
from datetime import datetime
import os

# Importações dos módulos do sistema
from config import AppConfig
from database import db
from models import Usuario, Produto, Cliente, Agendamento, Categoria, Venda
from backup_system import BackupSystem
from pdf_generator import PDFGenerator
from export_utils import ExportadorRelatorios
from auth import AuthManager, login_required, permission_required, admin_required, manager_or_admin_required
from functools import wraps


def criar_aplicacao():
    """
    Factory function para criar e configurar a aplicação Flask

    Returns:
        Flask: Instância configurada da aplicação
    """
    app = Flask(__name__,
                static_folder=AppConfig.STATIC_FOLDER,
                template_folder=AppConfig.TEMPLATE_FOLDER)

    # Configurações da aplicação
    app.secret_key = AppConfig.SECRET_KEY
    app.config['UPLOAD_FOLDER'] = AppConfig.UPLOAD_FOLDER
    app.config['MAX_CONTENT_LENGTH'] = AppConfig.MAX_CONTENT_LENGTH

    # Criar diretórios necessários
    _criar_diretorios_necessarios()

    return app


def _criar_diretorios_necessarios():
    """Cria os diretórios necessários para o funcionamento da aplicação"""
    diretorios = [
        AppConfig.UPLOAD_FOLDER,
        AppConfig.BACKUP_PATH
    ]

    for diretorio in diretorios:
        os.makedirs(diretorio, exist_ok=True)


# Criar instância da aplicação
app = criar_aplicacao()

# ============================================================================
# CONSTANTES E CONFIGURAÇÕES
# ============================================================================

class Mensagens:
    """Centraliza todas as mensagens do sistema"""

    # Mensagens de sucesso
    PRODUTO_CADASTRADO = 'Produto cadastrado com sucesso!'
    CLIENTE_CADASTRADO = 'Cliente cadastrado com sucesso!'
    AGENDAMENTO_CRIADO = 'Agendamento criado com sucesso!'
    VENDA_REALIZADA = 'Venda realizada com sucesso!'
    USUARIO_CADASTRADO = 'Usuário cadastrado com sucesso!'
    LOGIN_SUCESSO = 'Login realizado com sucesso!'
    LOGOUT_SUCESSO = 'Logout realizado com sucesso!'

    # Mensagens de erro
    DADOS_INVALIDOS = 'Erro nos dados informados: {}'
    ERRO_INTERNO = 'Erro interno: {}'
    CREDENCIAIS_INVALIDAS = 'Email ou senha incorretos!'
    CAMPOS_OBRIGATORIOS = 'Email e senha são obrigatórios!'
    DADOS_NAO_FORNECIDOS = 'Dados não fornecidos'
    ACESSO_NEGADO = 'Acesso negado! Apenas administradores podem acessar esta área.'


# ============================================================================
# DECORADORES E UTILITÁRIOS
# ============================================================================

def tratar_erros_formulario(template_erro, dados_extras_func=None):
    """
    Decorador para tratamento padronizado de erros em formulários

    Args:
        template_erro (str): Template a ser renderizado em caso de erro
        dados_extras_func (callable, optional): Função para obter dados extras para o template
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ValueError as e:
                flash(Mensagens.DADOS_INVALIDOS.format(str(e)), 'error')
                dados_extras = dados_extras_func() if dados_extras_func else {}
                return render_template(template_erro, **dados_extras)
            except Exception as e:
                flash(Mensagens.ERRO_INTERNO.format(str(e)), 'error')
                dados_extras = dados_extras_func() if dados_extras_func else {}
                return render_template(template_erro, **dados_extras)
        return wrapper
    return decorator



def obter_dados_formulario_cadastro():
    """
    Obtém dados extras necessários para formulários de cadastro

    Returns:
        dict: Dados para os templates de formulário
    """
    return {
        'categorias': Categoria.listar(),
        'clientes': Cliente.listar(),
        'produtos': Produto.listar(),
        'vendedores': Usuario.listar_vendedores()
    }


def validar_campos_obrigatorios(campos):
    """
    Valida se campos obrigatórios foram fornecidos

    Args:
        campos (dict): Dicionário com nome_campo: valor

    Raises:
        ValueError: Se algum campo obrigatório estiver vazio
    """
    for nome_campo, valor in campos.items():
        if not valor or (isinstance(valor, str) and not valor.strip()):
            raise ValueError(f'{nome_campo} é obrigatório')


def executar_consulta_unica(query, params=None):
    """
    Executa uma consulta SQL que retorna um único valor

    Args:
        query (str): Query SQL
        params (tuple, optional): Parâmetros da query

    Returns:
        Any: Resultado da consulta
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        cursor.execute(query, params or ())
        return cursor.fetchone()
    finally:
        conn.close()


def executar_consulta_multipla(query, params=None):
    """
    Executa uma consulta SQL que retorna múltiplos resultados

    Args:
        query (str): Query SQL
        params (tuple, optional): Parâmetros da query

    Returns:
        list: Lista de resultados
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        cursor.execute(query, params or ())
        return [dict(row) for row in cursor.fetchall()]
    finally:
        conn.close()


# ============================================================================
# MIDDLEWARE E CONFIGURAÇÕES DE CONTEXTO
# ============================================================================

@app.before_request
def verificar_autenticacao():
    """
    Middleware para verificar autenticação do usuário usando o novo sistema
    Redireciona para login se não autenticado
    """
    # Permitir acesso às rotas públicas
    if request.endpoint and any(rota in request.endpoint for rota in AuthManager.ROTAS_PUBLICAS):
        return

    # Verificar se usuário está logado e sessão é válida
    if not AuthManager.verificar_sessao_valida():
        # Salvar URL solicitada para redirecionar após login
        if request.endpoint != 'login':
            session['next_url'] = request.url
        return redirect(url_for('login'))


@app.context_processor
def injetar_dados_usuario():
    """
    Context processor para disponibilizar dados do usuário em todos os templates

    Returns:
        dict: Dicionário com dados do usuário logado e permissões
    """
    usuario = AuthManager.usuario_logado()
    if usuario:
        return {
            'usuario_logado': usuario,
            'user_permissions': AuthManager.PERMISSOES.get(usuario.tipo, []),
            'auth_manager': AuthManager
        }

    return {'usuario_logado': None, 'user_permissions': [], 'auth_manager': AuthManager}

# ============================================================================
# ROTAS DE AUTENTICAÇÃO E DASHBOARD
# ============================================================================

@app.route('/')
@login_required
def index():
    """
    Página inicial - Dashboard principal do sistema

    Returns:
        str: Template renderizado do dashboard com estatísticas
    """

    estatisticas = _obter_estatisticas_dashboard()
    ultimos_agendamentos = _obter_ultimos_agendamentos()

    return render_template('dashboard.html',
                         estatisticas=estatisticas,
                         ultimos_agendamentos=ultimos_agendamentos)


def _obter_estatisticas_dashboard():
    """
    Obtém estatísticas para exibição no dashboard

    Returns:
        dict: Dicionário com estatísticas do sistema
    """
    hoje = datetime.now().strftime('%Y-%m-%d')

    # Definir consultas de estatísticas
    consultas = {
        'total_produtos': ('SELECT COUNT(*) as total FROM produtos WHERE ativo = 1', None),
        'total_clientes': ('SELECT COUNT(*) as total FROM clientes', None),
        'agendamentos_hoje': ('''
            SELECT COUNT(*) as total FROM agendamentos
            WHERE DATE(data_agendamento) = ? AND status = 'agendado'
        ''', (hoje,)),
        'estoque_baixo': ('''
            SELECT COUNT(*) as total FROM produtos
            WHERE estoque_atual <= estoque_minimo AND ativo = 1
        ''', None)
    }

    # Executar consultas e coletar resultados
    estatisticas = {}
    for chave, (query, params) in consultas.items():
        resultado = executar_consulta_unica(query, params)
        estatisticas[chave] = resultado['total']

    return estatisticas


def _obter_ultimos_agendamentos():
    """
    Obtém os últimos agendamentos para exibição no dashboard

    Returns:
        list: Lista com os últimos 5 agendamentos
    """
    query = '''
        SELECT a.*, c.nome as cliente_nome, p.nome as produto_nome, u.nome as vendedor_nome
        FROM agendamentos a
        JOIN clientes c ON a.cliente_id = c.id
        JOIN produtos p ON a.produto_id = p.id
        JOIN usuarios u ON a.vendedor_id = u.id
        ORDER BY a.data_agendamento DESC
        LIMIT 5
    '''

    return executar_consulta_multipla(query)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """
    Página de login do sistema

    Returns:
        str: Template de login ou redirecionamento para dashboard
    """
    # Se já está logado, redirecionar para dashboard
    if AuthManager.verificar_sessao_valida():
        return redirect(url_for('index'))

    if request.method == 'POST':
        return _processar_login()

    return render_template('login.html')


def _processar_login():
    """
    Processa o formulário de login usando o novo sistema de autenticação

    Returns:
        Response: Redirecionamento ou template de login
    """
    email = request.form.get('email', '').strip()
    senha = request.form.get('senha', '')
    lembrar = request.form.get('lembrar') == 'on'

    # Validações básicas
    if not email or not senha:
        flash(Mensagens.CAMPOS_OBRIGATORIOS, 'error')
        return render_template('login.html')

    # Tentar fazer login
    sucesso, mensagem, usuario = AuthManager.fazer_login(email, senha, lembrar)

    if sucesso:
        flash(mensagem, 'success')
        # Redirecionar para página solicitada ou dashboard
        next_page = request.args.get('next')
        return redirect(next_page) if next_page else redirect(url_for('index'))
    else:
        flash(mensagem, 'error')
        return render_template('login.html')


@app.route('/logout')
def logout():
    """
    Realiza logout do usuário usando o novo sistema de autenticação

    Returns:
        Response: Redirecionamento para página de login
    """
    mensagem = AuthManager.fazer_logout()
    flash(mensagem, 'success')
    return redirect(url_for('login'))

# ============================================================================
# ROTAS DE PRODUTOS
# ============================================================================

@app.route('/produtos')
def produtos():
    """
    Lista todos os produtos cadastrados

    Returns:
        str: Template com lista de produtos e categorias
    """
    produtos_lista = Produto.listar()
    categorias = Categoria.listar()

    return render_template('produtos/lista.html',
                         produtos=produtos_lista,
                         categorias=categorias)


@app.route('/produtos/novo', methods=['GET', 'POST'])
def produto_novo():
    """
    Cadastro de novo produto

    Returns:
        str: Template do formulário ou redirecionamento
    """
    if request.method == 'POST':
        return _processar_cadastro_produto()

    categorias = Categoria.listar()
    return render_template('produtos/form.html', categorias=categorias)


@tratar_erros_formulario('produtos/form.html', lambda: {'categorias': Categoria.listar()})
def _processar_cadastro_produto():
    """
    Processa o formulário de cadastro de produto

    Returns:
        Response: Redirecionamento para lista de produtos
    """
    # Extrair dados do formulário
    dados_produto = _extrair_dados_produto_formulario()

    # Criar produto
    Produto.criar(**dados_produto)

    flash(Mensagens.PRODUTO_CADASTRADO, 'success')
    return redirect(url_for('produtos'))


def _extrair_dados_produto_formulario():
    """
    Extrai e valida dados do produto do formulário

    Returns:
        dict: Dados do produto validados

    Raises:
        ValueError: Se dados inválidos
    """
    nome = request.form.get('nome', '').strip()
    descricao = request.form.get('descricao', '').strip()

    # Validar campos obrigatórios
    validar_campos_obrigatorios({'Nome do produto': nome})

    # Extrair e validar valores numéricos
    try:
        preco_custo = float(request.form['preco_custo'])
        lucro_desejado = float(request.form['lucro_desejado'])
        estoque_atual = int(request.form['estoque_atual'])
        estoque_minimo = int(request.form['estoque_minimo'])

    except (ValueError, KeyError):
        raise ValueError('Valores numéricos inválidos')

    # Validações de negócio
    _validar_regras_negocio_produto(preco_custo, estoque_atual, estoque_minimo)

    # Extrair dados opcionais
    tipo_lucro = request.form.get('tipo_lucro')
    categoria_id = request.form.get('categoria_id')
    categoria_id = int(categoria_id) if categoria_id else None

    return {
        'nome': nome,
        'descricao': descricao,
        'preco_custo': preco_custo,
        'lucro_desejado': lucro_desejado,
        'tipo_lucro': tipo_lucro,
        'categoria_id': categoria_id,
        'estoque_atual': estoque_atual,
        'estoque_minimo': estoque_minimo
    }


def _validar_regras_negocio_produto(preco_custo, estoque_atual, estoque_minimo):
    """
    Valida regras de negócio específicas para produtos

    Args:
        preco_custo (float): Preço de custo
        estoque_atual (int): Estoque atual
        estoque_minimo (int): Estoque mínimo

    Raises:
        ValueError: Se regras de negócio violadas
    """
    if preco_custo <= 0:
        raise ValueError('Preço de custo deve ser maior que zero')

    if estoque_atual < 0 or estoque_minimo < 0:
        raise ValueError('Estoque não pode ser negativo')

# ============================================================================
# ROTAS DE CLIENTES
# ============================================================================

@app.route('/clientes')
def clientes():
    """
    Lista todos os clientes cadastrados

    Returns:
        str: Template com lista de clientes
    """
    clientes_lista = Cliente.listar()
    return render_template('clientes/lista.html', clientes=clientes_lista)


@app.route('/clientes/novo', methods=['GET', 'POST'])
def cliente_novo():
    """
    Cadastro de novo cliente

    Returns:
        str: Template do formulário ou redirecionamento
    """
    if request.method == 'POST':
        return _processar_cadastro_cliente()

    return render_template('clientes/form.html')


@tratar_erros_formulario('clientes/form.html')
def _processar_cadastro_cliente():
    """
    Processa o formulário de cadastro de cliente

    Returns:
        Response: Redirecionamento para lista de clientes
    """
    # Extrair dados do formulário
    dados_cliente = _extrair_dados_cliente_formulario()

    # Criar cliente
    Cliente.criar(**dados_cliente)

    flash(Mensagens.CLIENTE_CADASTRADO, 'success')
    return redirect(url_for('clientes'))


def _extrair_dados_cliente_formulario():
    """
    Extrai e valida dados do cliente do formulário

    Returns:
        dict: Dados do cliente validados

    Raises:
        ValueError: Se dados inválidos
    """
    nome = request.form.get('nome', '').strip()
    telefone = request.form.get('telefone', '').strip()

    # Validar campos obrigatórios
    validar_campos_obrigatorios({
        'Nome do cliente': nome,
        'Telefone do cliente': telefone
    })

    # Extrair dados opcionais
    dados_opcionais = _extrair_dados_endereco_formulario()
    email = request.form.get('email', '').strip() or None

    return {
        'nome': nome,
        'telefone': telefone,
        'email': email,
        **dados_opcionais
    }


def _extrair_dados_endereco_formulario():
    """
    Extrai dados de endereço do formulário

    Returns:
        dict: Dados de endereço
    """
    return {
        'logradouro': request.form.get('logradouro', '').strip() or None,
        'numero': request.form.get('numero', '').strip() or None,
        'bairro': request.form.get('bairro', '').strip() or None,
        'cidade': request.form.get('cidade', '').strip() or None,
        'uf': request.form.get('uf', '').strip() or None,
        'cep': request.form.get('cep', '').strip() or None
    }

# ============================================================================
# ROTAS DE AGENDAMENTOS
# ============================================================================

@app.route('/agendamentos')
def agendamentos():
    """
    Lista todos os agendamentos cadastrados

    Returns:
        str: Template com lista de agendamentos
    """
    agendamentos_lista = Agendamento.listar()
    return render_template('agendamentos/lista.html', agendamentos=agendamentos_lista)


@app.route('/agendamentos/calendario')
def agendamentos_calendario():
    """
    Exibe calendário de agendamentos

    Returns:
        str: Template do calendário de agendamentos
    """
    return render_template('agendamentos/calendario.html')


@app.route('/agendamentos/novo', methods=['GET', 'POST'])
def agendamento_novo():
    """
    Cadastro de novo agendamento

    Returns:
        str: Template do formulário ou resposta JSON
    """
    if request.method == 'POST':
        # Verificar se é requisição JSON (do calendário)
        if request.is_json:
            return _processar_agendamento_json()
        else:
            return _processar_cadastro_agendamento()

    # Carregar dados para o formulário
    clientes_lista = Cliente.listar()
    produtos_lista = Produto.listar()
    vendedores = Usuario.listar_vendedores()

    return render_template('agendamentos/form.html',
                         clientes=clientes_lista,
                         produtos=produtos_lista,
                         vendedores=vendedores)


def _processar_cadastro_agendamento():
    """
    Processa o formulário de cadastro de agendamento

    Returns:
        Response: Redirecionamento para lista de agendamentos
    """
    try:
        # Extrair dados do formulário
        dados_agendamento = _extrair_dados_agendamento_formulario()

        # Criar agendamento
        Agendamento.criar(**dados_agendamento)

        flash('Agendamento criado com sucesso!', 'success')
        return redirect(url_for('agendamentos'))

    except ValueError as e:
        flash(f'Erro nos dados informados: {str(e)}', 'error')
        return _renderizar_formulario_agendamento()

    except Exception as e:
        flash(f'Erro ao criar agendamento: {str(e)}', 'error')
        return _renderizar_formulario_agendamento()


def _processar_agendamento_json():
    """
    Processa criação de agendamento via JSON (do calendário)

    Returns:
        Response: JSON com resultado da operação
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Dados não fornecidos'
            }), 400

        # Validar dados obrigatórios
        cliente_id = data.get('cliente_id')
        produto_id = data.get('produto_id')
        vendedor_id = data.get('vendedor_id')
        data_agendamento = data.get('data_agendamento')

        if not all([cliente_id, produto_id, vendedor_id, data_agendamento]):
            return jsonify({
                'success': False,
                'message': 'Todos os campos são obrigatórios'
            }), 400

        # Criar agendamento
        agendamento_id = Agendamento.criar(
            cliente_id=cliente_id,
            produto_id=produto_id,
            vendedor_id=vendedor_id,
            data_agendamento=data_agendamento,
            observacoes=data.get('observacoes')
        )

        return jsonify({
            'success': True,
            'message': 'Agendamento criado com sucesso!',
            'agendamento_id': agendamento_id
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao criar agendamento: {str(e)}'
        }), 500


def _extrair_dados_agendamento_formulario():
    """
    Extrai e valida dados do agendamento do formulário

    Returns:
        dict: Dados do agendamento validados

    Raises:
        ValueError: Se dados inválidos
    """
    try:
        cliente_id = int(request.form['cliente_id'])
        produto_id = int(request.form['produto_id'])
        vendedor_id = int(request.form['vendedor_id'])
        data_agendamento = request.form['data_agendamento']

    except (ValueError, KeyError) as e:
        raise ValueError('Dados obrigatórios não informados ou inválidos')

    # Validar se data não está vazia
    if not data_agendamento:
        raise ValueError('Data do agendamento é obrigatória')

    observacoes = request.form.get('observacoes', '').strip() or None

    return {
        'cliente_id': cliente_id,
        'produto_id': produto_id,
        'vendedor_id': vendedor_id,
        'data_agendamento': data_agendamento,
        'observacoes': observacoes
    }


def _renderizar_formulario_agendamento():
    """
    Renderiza o formulário de agendamento com dados necessários

    Returns:
        str: Template do formulário de agendamento
    """
    clientes_lista = Cliente.listar()
    produtos_lista = Produto.listar()
    vendedores = Usuario.listar_vendedores()

    return render_template('agendamentos/form.html',
                         clientes=clientes_lista,
                         produtos=produtos_lista,
                         vendedores=vendedores)

# ============================================================================
# ROTAS DE VENDAS
# ============================================================================

@app.route('/vendas')
def vendas():
    """
    Lista todas as vendas realizadas

    Returns:
        str: Template com lista de vendas
    """
    vendas_lista = Venda.listar()
    return render_template('vendas/lista.html', vendas=vendas_lista)


@app.route('/vendas/nova', methods=['GET', 'POST'])
def venda_nova():
    """
    Cadastro de nova venda

    Returns:
        str: Template do formulário ou resposta JSON
    """
    if request.method == 'POST':
        return _processar_nova_venda()

    # Carregar dados para o formulário
    clientes_lista = Cliente.listar()
    produtos_lista = Produto.listar()

    return render_template('vendas/form.html',
                         clientes=clientes_lista,
                         produtos=produtos_lista)


def _processar_nova_venda():
    """
    Processa a criação de uma nova venda via JSON

    Returns:
        Response: Resposta JSON com resultado da operação
    """
    try:
        # Extrair dados JSON
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': 'Dados não fornecidos'
            }), 400

        # Validar dados obrigatórios
        dados_venda = _validar_dados_venda(data)

        # Criar venda
        venda_id = Venda.criar(**dados_venda)

        return jsonify({
            'success': True,
            'message': 'Venda realizada com sucesso!',
            'venda_id': venda_id
        })

    except ValueError as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro interno: {str(e)}'
        }), 500


def _validar_dados_venda(data):
    """
    Valida os dados da venda

    Args:
        data (dict): Dados da venda

    Returns:
        dict: Dados validados

    Raises:
        ValueError: Se dados inválidos
    """
    cliente_id = data.get('cliente_id')
    itens = data.get('itens', [])

    # Validações obrigatórias
    if not cliente_id:
        raise ValueError('Cliente é obrigatório')

    if not itens:
        raise ValueError('Pelo menos um item é obrigatório')

    # Dados opcionais
    vendedor_id = session.get('user_id')
    desconto_total = data.get('desconto_total', 0)
    observacoes = data.get('observacoes')

    return {
        'cliente_id': cliente_id,
        'vendedor_id': vendedor_id,
        'itens': itens,
        'desconto_total': desconto_total,
        'observacoes': observacoes
    }

@app.route('/vendas/<int:venda_id>')
def venda_detalhes(venda_id):
    """
    Exibe detalhes de uma venda específica

    Args:
        venda_id (int): ID da venda

    Returns:
        str: Template com detalhes da venda
    """
    venda = Venda.buscar_por_id(venda_id)

    if not venda:
        flash('Venda não encontrada!', 'error')
        return redirect(url_for('vendas'))

    return render_template('vendas/detalhes.html', venda=venda)


@app.route('/vendas/<int:venda_id>/comprovante')
def gerar_comprovante(venda_id):
    """
    Gera comprovante da venda em PDF ou HTML

    Args:
        venda_id (int): ID da venda

    Returns:
        Response: Arquivo PDF ou template HTML
    """
    venda = Venda.buscar_por_id(venda_id)

    if not venda:
        flash('Venda não encontrada!', 'error')
        return redirect(url_for('vendas'))

    formato = request.args.get('formato', 'html')

    if formato == 'pdf':
        return _gerar_comprovante_pdf(venda, venda_id)

    # Exibir comprovante em HTML
    return render_template('vendas/comprovante.html', venda=venda)


@app.route('/vendas/<int:venda_id>/cancelar', methods=['POST'])
def cancelar_venda(venda_id):
    """
    Cancela uma venda e retorna produtos ao estoque

    Args:
        venda_id (int): ID da venda a ser cancelada

    Returns:
        Response: Redirecionamento com mensagem de sucesso ou erro
    """
    # Verificar permissões
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash(Mensagens.ACESSO_NEGADO, 'error')
        return redirect(url_for('vendas'))

    try:
        # Buscar venda
        venda = Venda.buscar_por_id(venda_id)
        if not venda:
            flash('Venda não encontrada!', 'error')
            return redirect(url_for('vendas'))

        # Verificar se já foi cancelada
        if 'CANCELADA' in (venda.get('observacoes') or ''):
            flash('Esta venda já foi cancelada!', 'warning')
            return redirect(url_for('vendas'))

        # Cancelar venda e retornar estoque
        _processar_cancelamento_venda(venda_id, venda['itens'])

        flash(f'Venda #{venda_id} cancelada com sucesso! Produtos retornados ao estoque.', 'success')

    except Exception as e:
        flash(f'Erro ao cancelar venda: {str(e)}', 'error')

    return redirect(url_for('vendas'))


def _gerar_comprovante_pdf(venda, venda_id):
    """
    Gera comprovante em PDF

    Args:
        venda: Objeto da venda
        venda_id (int): ID da venda

    Returns:
        Response: Arquivo PDF ou redirecionamento em caso de erro
    """
    try:
        pdf_generator = PDFGenerator()
        pdf_path = pdf_generator.gerar_comprovante_venda(venda)

        return send_from_directory(
            os.path.dirname(pdf_path),
            os.path.basename(pdf_path),
            as_attachment=True,
            download_name=f'comprovante_venda_{venda_id}.pdf'
        )

    except Exception as e:
        flash(f'Erro ao gerar PDF: {str(e)}', 'error')
        return redirect(url_for('venda_detalhes', venda_id=venda_id))


def _processar_cancelamento_venda(venda_id, itens):
    """
    Processa o cancelamento de uma venda retornando produtos ao estoque

    Args:
        venda_id (int): ID da venda
        itens (list): Lista de itens da venda
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        # Retornar produtos ao estoque
        for item in itens:
            cursor.execute('''
                UPDATE produtos
                SET estoque_atual = estoque_atual + ?
                WHERE id = ?
            ''', (item['quantidade'], item['produto_id']))

        # Marcar venda como cancelada
        cursor.execute('''
            UPDATE vendas
            SET observacoes = COALESCE(observacoes, '') || ' [VENDA CANCELADA]'
            WHERE id = ?
        ''', (venda_id,))

        # Registrar log de auditoria
        cursor.execute('''
            INSERT INTO logs_auditoria (usuario_id, acao, tabela, registro_id, detalhes)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            session.get('user_id'),
            'CANCELAMENTO_VENDA',
            'vendas',
            venda_id,
            f'Venda #{venda_id} cancelada - produtos retornados ao estoque'
        ))

        conn.commit()

    except Exception as e:
        conn.rollback()
        raise e
    finally:
        conn.close()

# ============================================================================
# ROTAS DE RELATÓRIOS
# ============================================================================

@app.route('/relatorios')
def relatorios():
    """
    Dashboard principal de relatórios

    Returns:
        str: Template do dashboard de relatórios
    """
    return render_template('relatorios/dashboard.html')


@app.route('/relatorios/estoque-baixo')
def relatorio_estoque_baixo():
    """
    Relatório de produtos com estoque baixo

    Returns:
        str: Template com produtos em estoque baixo
    """
    produtos_baixo_estoque = _obter_produtos_estoque_baixo()
    return render_template('relatorios/estoque_baixo.html', produtos=produtos_baixo_estoque)


def _obter_produtos_estoque_baixo():
    """
    Obtém produtos com estoque abaixo do mínimo

    Returns:
        list: Lista de produtos com estoque baixo
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT p.*, c.nome as categoria_nome
            FROM produtos p
            LEFT JOIN categorias c ON p.categoria_id = c.id
            WHERE p.estoque_atual <= p.estoque_minimo AND p.ativo = 1
            ORDER BY p.estoque_atual ASC
        ''')

        return [dict(row) for row in cursor.fetchall()]

    finally:
        conn.close()

@app.route('/relatorios/vendas-periodo')
def relatorio_vendas_periodo():
    """
    Relatório de vendas por período com filtros

    Returns:
        str: Template do relatório de vendas por período
    """
    filtros = _extrair_filtros_vendas_periodo()
    vendas = _obter_vendas_por_periodo(filtros)
    estatisticas = _calcular_estatisticas_vendas(vendas)
    vendedores = _obter_lista_vendedores()

    return render_template('relatorios/vendas_periodo.html',
                         vendas=vendas,
                         estatisticas=estatisticas,
                         vendedores=vendedores,
                         filtros=filtros)


def _extrair_filtros_vendas_periodo():
    """
    Extrai filtros da URL para relatório de vendas

    Returns:
        dict: Dicionário com filtros aplicados
    """
    return {
        'data_inicio': request.args.get('data_inicio'),
        'data_fim': request.args.get('data_fim'),
        'vendedor_id': request.args.get('vendedor_id')
    }


def _obter_vendas_por_periodo(filtros):
    """
    Obtém vendas filtradas por período

    Args:
        filtros (dict): Filtros a serem aplicados

    Returns:
        list: Lista de vendas filtradas
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        # Query base
        query = '''
            SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
                   u.nome as vendedor_nome, DATE(v.data_venda) as data_venda_formatada
            FROM vendas v
            JOIN clientes c ON v.cliente_id = c.id
            JOIN usuarios u ON v.vendedor_id = u.id
            WHERE 1=1
        '''
        params = []

        # Aplicar filtros
        if filtros['data_inicio']:
            query += ' AND DATE(v.data_venda) >= ?'
            params.append(filtros['data_inicio'])

        if filtros['data_fim']:
            query += ' AND DATE(v.data_venda) <= ?'
            params.append(filtros['data_fim'])

        if filtros['vendedor_id']:
            query += ' AND v.vendedor_id = ?'
            params.append(filtros['vendedor_id'])

        query += ' ORDER BY v.data_venda DESC'

        cursor.execute(query, params)
        return [dict(row) for row in cursor.fetchall()]

    finally:
        conn.close()


def _calcular_estatisticas_vendas(vendas):
    """
    Calcula estatísticas das vendas

    Args:
        vendas (list): Lista de vendas

    Returns:
        dict: Estatísticas calculadas
    """
    total_vendas = len(vendas)
    total_faturamento = sum(venda['total_final'] for venda in vendas)
    ticket_medio = total_faturamento / total_vendas if total_vendas > 0 else 0

    return {
        'total_vendas': total_vendas,
        'total_faturamento': total_faturamento,
        'ticket_medio': ticket_medio
    }


def _obter_lista_vendedores():
    """
    Obtém lista de vendedores para filtros

    Returns:
        list: Lista de vendedores
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('''
            SELECT id, nome FROM usuarios
            WHERE tipo IN ("vendedor", "gerente", "admin")
            ORDER BY nome
        ''')
        return [dict(row) for row in cursor.fetchall()]

    finally:
        conn.close()

@app.route('/relatorios/produtos-mais-vendidos')
def relatorio_produtos_mais_vendidos():
    """Relatório de produtos mais vendidos"""
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    categoria_id = request.args.get('categoria_id')

    conn = db.get_connection()
    cursor = conn.cursor()

    # Query para produtos mais vendidos
    query = '''
        SELECT p.id, p.nome as produto_nome, c.nome as categoria_nome,
               SUM(iv.quantidade) as quantidade_vendida,
               SUM(iv.subtotal_item) as receita_bruta,
               COUNT(DISTINCT v.id) as numero_vendas,
               AVG(iv.preco_unitario) as preco_medio
        FROM itens_venda iv
        JOIN produtos p ON iv.produto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        JOIN vendas v ON iv.venda_id = v.id
        WHERE 1=1
    '''
    params = []

    # Filtros
    if data_inicio:
        query += ' AND DATE(v.data_venda) >= ?'
        params.append(data_inicio)

    if data_fim:
        query += ' AND DATE(v.data_venda) <= ?'
        params.append(data_fim)

    if categoria_id:
        query += ' AND p.categoria_id = ?'
        params.append(categoria_id)

    query += '''
        GROUP BY p.id, p.nome, c.nome
        ORDER BY quantidade_vendida DESC
        LIMIT 20
    '''

    cursor.execute(query, params)
    produtos = [dict(row) for row in cursor.fetchall()]

    # Categorias para filtro
    cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
    categorias = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('relatorios/produtos_mais_vendidos.html',
                         produtos=produtos,
                         categorias=categorias,
                         filtros={
                             'data_inicio': data_inicio,
                             'data_fim': data_fim,
                             'categoria_id': categoria_id
                         })

@app.route('/relatorios/vendas-por-vendedor')
def relatorio_vendas_por_vendedor():
    """Relatório de vendas por vendedor"""
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')

    conn = db.get_connection()
    cursor = conn.cursor()

    # Query para vendas por vendedor
    query = '''
        SELECT u.id, u.nome as vendedor_nome,
               COUNT(v.id) as quantidade_vendas,
               SUM(v.total_final) as valor_total,
               AVG(v.total_final) as ticket_medio,
               MIN(v.data_venda) as primeira_venda,
               MAX(v.data_venda) as ultima_venda
        FROM usuarios u
        LEFT JOIN vendas v ON u.id = v.vendedor_id
    '''
    params = []

    # Filtros
    where_conditions = ['u.tipo IN ("vendedor", "gerente", "admin")']

    if data_inicio:
        where_conditions.append('(v.data_venda IS NULL OR DATE(v.data_venda) >= ?)')
        params.append(data_inicio)

    if data_fim:
        where_conditions.append('(v.data_venda IS NULL OR DATE(v.data_venda) <= ?)')
        params.append(data_fim)

    if where_conditions:
        query += ' WHERE ' + ' AND '.join(where_conditions)

    query += '''
        GROUP BY u.id, u.nome
        ORDER BY valor_total DESC NULLS LAST
    '''

    cursor.execute(query, params)
    vendedores = [dict(row) for row in cursor.fetchall()]

    # Calcular totais gerais
    total_vendas = sum(v['quantidade_vendas'] or 0 for v in vendedores)
    total_faturamento = sum(v['valor_total'] or 0 for v in vendedores)

    conn.close()

    estatisticas = {
        'total_vendas': total_vendas,
        'total_faturamento': total_faturamento,
        'total_vendedores': len([v for v in vendedores if v['quantidade_vendas'] and v['quantidade_vendas'] > 0])
    }

    return render_template('relatorios/vendas_por_vendedor.html',
                         vendedores=vendedores,
                         estatisticas=estatisticas,
                         filtros={
                             'data_inicio': data_inicio,
                             'data_fim': data_fim
                         })

@app.route('/relatorios/lucro')
def relatorio_lucro():
    """Relatório de lucro"""
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    produto_id = request.args.get('produto_id')

    conn = db.get_connection()
    cursor = conn.cursor()

    # Query para cálculo de lucro
    query = '''
        SELECT v.id as venda_id, v.data_venda, c.nome as cliente_nome,
               u.nome as vendedor_nome, p.nome as produto_nome,
               iv.quantidade, iv.preco_unitario, iv.desconto_item,
               p.preco_custo, iv.subtotal_item,
               (iv.preco_unitario - p.preco_custo) * iv.quantidade - iv.desconto_item as lucro_bruto,
               ((iv.preco_unitario - p.preco_custo) * iv.quantidade - iv.desconto_item) / iv.subtotal_item * 100 as margem_lucro
        FROM vendas v
        JOIN itens_venda iv ON v.id = iv.venda_id
        JOIN produtos p ON iv.produto_id = p.id
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        WHERE 1=1
    '''
    params = []

    # Filtros
    if data_inicio:
        query += ' AND DATE(v.data_venda) >= ?'
        params.append(data_inicio)

    if data_fim:
        query += ' AND DATE(v.data_venda) <= ?'
        params.append(data_fim)

    if produto_id:
        query += ' AND p.id = ?'
        params.append(produto_id)

    query += ' ORDER BY v.data_venda DESC'

    cursor.execute(query, params)
    vendas_detalhadas = [dict(row) for row in cursor.fetchall()]

    # Resumo de lucro
    total_receita = sum(venda['subtotal_item'] for venda in vendas_detalhadas)
    total_lucro = sum(venda['lucro_bruto'] for venda in vendas_detalhadas)
    margem_media = (total_lucro / total_receita * 100) if total_receita > 0 else 0

    # Produtos para filtro
    cursor.execute('SELECT id, nome FROM produtos WHERE ativo = 1 ORDER BY nome')
    produtos = [dict(row) for row in cursor.fetchall()]

    conn.close()

    estatisticas = {
        'total_receita': total_receita,
        'total_lucro': total_lucro,
        'margem_media': margem_media,
        'total_vendas': len(set(venda['venda_id'] for venda in vendas_detalhadas))
    }

    return render_template('relatorios/lucro.html',
                         vendas=vendas_detalhadas,
                         estatisticas=estatisticas,
                         produtos=produtos,
                         filtros={
                             'data_inicio': data_inicio,
                             'data_fim': data_fim,
                             'produto_id': produto_id
                         })


# ============================================================================
# ROTAS DE EXPORTAÇÃO
# ============================================================================

@app.route('/relatorios/vendas-periodo/exportar-pdf')
def exportar_vendas_periodo_pdf():
    """
    Exporta relatório de vendas por período em PDF

    Returns:
        Response: Arquivo PDF para download
    """
    try:
        # Obter parâmetros
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')
        vendedor_id = request.args.get('vendedor_id')

        if not data_inicio or not data_fim:
            flash('Período é obrigatório para exportação', 'error')
            return redirect(url_for('relatorio_vendas_periodo'))

        # Buscar dados do relatório
        vendas, estatisticas = _obter_dados_relatorio_vendas_periodo(
            data_inicio, data_fim, vendedor_id
        )

        # Exportar PDF
        exportador = ExportadorRelatorios()
        return exportador.exportar_vendas_periodo_pdf(
            vendas, data_inicio, data_fim, estatisticas
        )

    except Exception as e:
        flash(f'Erro ao exportar relatório: {str(e)}', 'error')
        return redirect(url_for('relatorio_vendas_periodo'))


@app.route('/relatorios/vendas-periodo/exportar-excel')
def exportar_vendas_periodo_excel():
    """
    Exporta relatório de vendas por período em Excel

    Returns:
        Response: Arquivo Excel para download
    """
    try:
        # Obter parâmetros
        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')
        vendedor_id = request.args.get('vendedor_id')

        if not data_inicio or not data_fim:
            flash('Período é obrigatório para exportação', 'error')
            return redirect(url_for('relatorio_vendas_periodo'))

        # Buscar dados do relatório
        vendas, estatisticas = _obter_dados_relatorio_vendas_periodo(
            data_inicio, data_fim, vendedor_id
        )

        # Exportar Excel
        exportador = ExportadorRelatorios()
        return exportador.exportar_vendas_periodo_excel(
            vendas, data_inicio, data_fim, estatisticas
        )

    except Exception as e:
        flash(f'Erro ao exportar relatório: {str(e)}', 'error')
        return redirect(url_for('relatorio_vendas_periodo'))


def _obter_dados_relatorio_vendas_periodo(data_inicio, data_fim, vendedor_id=None):
    """
    Obtém dados para relatório de vendas por período

    Args:
        data_inicio (str): Data de início
        data_fim (str): Data de fim
        vendedor_id (str, optional): ID do vendedor

    Returns:
        tuple: (vendas, estatisticas)
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        # Query base
        query = '''
            SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome,
                   DATE(v.data_venda) as data_venda
            FROM vendas v
            JOIN clientes c ON v.cliente_id = c.id
            JOIN usuarios u ON v.vendedor_id = u.id
            WHERE DATE(v.data_venda) BETWEEN ? AND ?
        '''
        params = [data_inicio, data_fim]

        # Filtro por vendedor
        if vendedor_id:
            query += ' AND v.vendedor_id = ?'
            params.append(vendedor_id)

        query += ' ORDER BY v.data_venda DESC'

        cursor.execute(query, params)
        vendas = [dict(row) for row in cursor.fetchall()]

        # Calcular estatísticas
        total_vendas = sum(venda['total_final'] for venda in vendas)
        qtd_vendas = len(vendas)
        ticket_medio = total_vendas / qtd_vendas if qtd_vendas > 0 else 0
        total_descontos = sum(venda['desconto_total'] for venda in vendas)

        estatisticas = {
            'total_vendas': total_vendas,
            'qtd_vendas': qtd_vendas,
            'ticket_medio': ticket_medio,
            'total_descontos': total_descontos
        }

        return vendas, estatisticas

    finally:
        conn.close()


@app.route('/relatorios/agendamentos-periodo')
def relatorio_agendamentos_periodo():
    """Relatório de agendamentos por período"""
    data_inicio = request.args.get('data_inicio')
    data_fim = request.args.get('data_fim')
    vendedor_id = request.args.get('vendedor_id')
    status = request.args.get('status')

    conn = db.get_connection()
    cursor = conn.cursor()

    # Query para agendamentos por período
    query = '''
        SELECT a.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
               p.nome as produto_nome, u.nome as vendedor_nome,
               DATE(a.data_agendamento) as data_agendamento_formatada
        FROM agendamentos a
        JOIN clientes c ON a.cliente_id = c.id
        JOIN produtos p ON a.produto_id = p.id
        JOIN usuarios u ON a.vendedor_id = u.id
        WHERE 1=1
    '''
    params = []

    # Filtros
    if data_inicio:
        query += ' AND DATE(a.data_agendamento) >= ?'
        params.append(data_inicio)

    if data_fim:
        query += ' AND DATE(a.data_agendamento) <= ?'
        params.append(data_fim)

    if vendedor_id:
        query += ' AND a.vendedor_id = ?'
        params.append(vendedor_id)

    if status:
        query += ' AND a.status = ?'
        params.append(status)

    query += ' ORDER BY a.data_agendamento DESC'

    cursor.execute(query, params)
    agendamentos = [dict(row) for row in cursor.fetchall()]

    # Estatísticas
    total_agendamentos = len(agendamentos)
    agendamentos_por_status = {}
    for agendamento in agendamentos:
        status_ag = agendamento['status']
        agendamentos_por_status[status_ag] = agendamentos_por_status.get(status_ag, 0) + 1

    # Vendedores para filtro
    cursor.execute('SELECT id, nome FROM usuarios WHERE tipo IN ("vendedor", "gerente", "admin") ORDER BY nome')
    vendedores = [dict(row) for row in cursor.fetchall()]

    conn.close()

    estatisticas = {
        'total_agendamentos': total_agendamentos,
        'agendamentos_por_status': agendamentos_por_status
    }

    return render_template('relatorios/agendamentos_periodo.html',
                         agendamentos=agendamentos,
                         estatisticas=estatisticas,
                         vendedores=vendedores,
                         filtros={
                             'data_inicio': data_inicio,
                             'data_fim': data_fim,
                             'vendedor_id': vendedor_id,
                             'status': status
                         })

# ============================================================================
# ROTAS DE USUÁRIOS (APENAS ADMINISTRADORES)
# ============================================================================

@app.route('/usuarios')
@admin_required
def usuarios():
    """
    Lista todos os usuários do sistema (apenas administradores)

    Returns:
        str: Template com lista de usuários
    """
    usuarios_lista = _obter_lista_usuarios()
    return render_template('usuarios/lista.html', usuarios=usuarios_lista)


@app.route('/usuarios/novo', methods=['GET', 'POST'])
@admin_required
def usuario_novo():
    """
    Cadastro de novo usuário (apenas administradores)

    Returns:
        str: Template do formulário ou redirecionamento
    """
    if request.method == 'POST':
        return _processar_cadastro_usuario()

    return render_template('usuarios/form.html')





def _obter_lista_usuarios():
    """
    Obtém lista de todos os usuários

    Returns:
        list: Lista de usuários
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        cursor.execute('SELECT * FROM usuarios ORDER BY nome')
        return [dict(row) for row in cursor.fetchall()]

    finally:
        conn.close()


def _processar_cadastro_usuario():
    """
    Processa o cadastro de novo usuário

    Returns:
        Response: Redirecionamento ou template com erro
    """
    try:
        # Extrair dados do formulário
        dados_usuario = _extrair_dados_usuario_formulario()

        # Criar usuário
        Usuario.criar(**dados_usuario)

        flash('Usuário cadastrado com sucesso!', 'success')
        return redirect(url_for('usuarios'))

    except ValueError as e:
        flash(f'Erro nos dados informados: {str(e)}', 'error')
        return render_template('usuarios/form.html')

    except Exception as e:
        flash(f'Erro ao cadastrar usuário: {str(e)}', 'error')
        return render_template('usuarios/form.html')


def _extrair_dados_usuario_formulario():
    """
    Extrai e valida dados do usuário do formulário

    Returns:
        dict: Dados do usuário validados

    Raises:
        ValueError: Se dados inválidos
    """
    nome = request.form.get('nome', '').strip()
    email = request.form.get('email', '').strip()
    senha = request.form.get('senha', '').strip()
    tipo = request.form.get('tipo', '').strip()

    # Validações obrigatórias
    if not nome:
        raise ValueError('Nome é obrigatório')

    if not email:
        raise ValueError('Email é obrigatório')

    if not senha:
        raise ValueError('Senha é obrigatória')

    if not tipo:
        raise ValueError('Tipo de usuário é obrigatório')

    return {
        'nome': nome,
        'email': email,
        'senha': senha,
        'tipo': tipo
    }

# ============================================================================
# APIs REST
# ============================================================================

@app.route('/api/calcular-preco', methods=['POST'])
def calcular_preco():
    """
    API para calcular preço de venda baseado no custo e lucro desejado

    Returns:
        Response: JSON com preço calculado ou erro
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'Dados não fornecidos'}), 400

        preco_venda = _calcular_preco_venda(data)

        return jsonify({'preco_venda': round(preco_venda, 2)})

    except (KeyError, ValueError, TypeError) as e:
        return jsonify({'error': f'Dados inválidos: {str(e)}'}), 400

    except Exception as e:
        return jsonify({'error': f'Erro interno: {str(e)}'}), 500


@app.route('/api/agendamentos')
def api_agendamentos():
    """
    API para fornecer agendamentos em formato JSON para o calendário

    Returns:
        Response: JSON com lista de agendamentos formatados para FullCalendar
    """
    try:
        agendamentos = Agendamento.listar()
        eventos = []

        for agendamento in agendamentos:
            # Mapear cores por status
            cores_status = {
                'agendado': '#ffc107',      # warning
                'confirmado': '#0d6efd',    # primary
                'realizado': '#198754',     # success
                'cancelado': '#dc3545'      # danger
            }

            evento = {
                'id': agendamento['id'],
                'title': f"{agendamento['cliente_nome']} - {agendamento['produto_nome']}",
                'start': agendamento['data_agendamento'],
                'backgroundColor': cores_status.get(agendamento['status'], '#6c757d'),
                'borderColor': cores_status.get(agendamento['status'], '#6c757d'),
                'className': f'fc-event-{agendamento["status"]}',
                'extendedProps': {
                    'cliente': agendamento['cliente_nome'],
                    'produto': agendamento['produto_nome'],
                    'vendedor': agendamento['vendedor_nome'],
                    'status': agendamento['status'],
                    'telefone': agendamento.get('cliente_telefone', ''),
                    'observacoes': agendamento.get('observacoes', '')
                }
            }
            eventos.append(evento)

        return jsonify(eventos)

    except Exception as e:
        return jsonify({'error': f'Erro ao carregar agendamentos: {str(e)}'}), 500


@app.route('/api/dashboard-stats')
def api_dashboard_stats():
    """
    API para fornecer estatísticas do dashboard em tempo real

    Returns:
        Response: JSON com estatísticas atualizadas
    """
    try:
        estatisticas = _obter_estatisticas_dashboard()
        return jsonify(estatisticas)

    except Exception as e:
        return jsonify({'error': f'Erro ao carregar estatísticas: {str(e)}'}), 500


@app.route('/api/alertas')
def api_alertas():
    """
    API para fornecer alertas do sistema

    Returns:
        Response: JSON com lista de alertas ativos
    """
    try:
        alertas = _gerar_alertas_sistema()
        return jsonify({'alertas': alertas})

    except Exception as e:
        return jsonify({'error': f'Erro ao carregar alertas: {str(e)}'}), 500


@app.route('/api/usuarios/vendedores')
def api_usuarios_vendedores():
    """
    API para listar vendedores

    Returns:
        Response: JSON com lista de vendedores
    """
    try:
        vendedores = Usuario.listar_vendedores()
        return jsonify(vendedores)

    except Exception as e:
        return jsonify({'error': f'Erro ao carregar vendedores: {str(e)}'}), 500


@app.route('/api/produtos')
def api_produtos():
    """
    API para listar produtos

    Returns:
        Response: JSON com lista de produtos
    """
    try:
        produtos = Produto.listar()
        return jsonify(produtos)

    except Exception as e:
        return jsonify({'error': f'Erro ao carregar produtos: {str(e)}'}), 500


@app.route('/api/clientes')
def api_clientes():
    """
    API para listar clientes

    Returns:
        Response: JSON com lista de clientes
    """
    try:
        clientes = Cliente.listar()
        return jsonify(clientes)

    except Exception as e:
        return jsonify({'error': f'Erro ao carregar clientes: {str(e)}'}), 500


def _calcular_preco_venda(data):
    """
    Calcula o preço de venda baseado nos parâmetros fornecidos

    Args:
        data (dict): Dados com preço de custo, lucro e tipo

    Returns:
        float: Preço de venda calculado

    Raises:
        KeyError: Se campos obrigatórios não fornecidos
        ValueError: Se valores inválidos
    """
    preco_custo = float(data['preco_custo'])
    lucro_desejado = float(data['lucro_desejado'])
    tipo_lucro = data['tipo_lucro']

    if preco_custo <= 0:
        raise ValueError('Preço de custo deve ser maior que zero')

    if lucro_desejado < 0:
        raise ValueError('Lucro desejado não pode ser negativo')

    if tipo_lucro == 'percentual':
        return preco_custo * (1 + lucro_desejado / 100)
    elif tipo_lucro == 'valor':
        return preco_custo + lucro_desejado
    else:
        raise ValueError('Tipo de lucro deve ser "percentual" ou "valor"')


@app.route('/api/dashboard-stats')
def dashboard_stats():
    """
    API para obter estatísticas do dashboard em tempo real

    Returns:
        Response: JSON com estatísticas atualizadas
    """
    try:
        estatisticas = _obter_estatisticas_api()

        return jsonify({
            **estatisticas,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': f'Erro ao obter estatísticas: {str(e)}'}), 500


def _obter_estatisticas_api():
    """
    Obtém estatísticas para a API do dashboard

    Returns:
        dict: Estatísticas do sistema
    """
    conn = db.get_connection()
    cursor = conn.cursor()

    try:
        # Total de produtos ativos
        cursor.execute('SELECT COUNT(*) as total FROM produtos WHERE ativo = 1')
        total_produtos = cursor.fetchone()['total']

        # Total de clientes
        cursor.execute('SELECT COUNT(*) as total FROM clientes')
        total_clientes = cursor.fetchone()['total']

        # Agendamentos de hoje
        hoje = datetime.now().strftime('%Y-%m-%d')
        cursor.execute('''
            SELECT COUNT(*) as total FROM agendamentos
            WHERE DATE(data_agendamento) = ? AND status = 'agendado'
        ''', (hoje,))
        agendamentos_hoje = cursor.fetchone()['total']

        # Produtos com estoque baixo
        cursor.execute('''
            SELECT COUNT(*) as total FROM produtos
            WHERE estoque_atual <= estoque_minimo AND ativo = 1
        ''')
        estoque_baixo = cursor.fetchone()['total']

        return {
            'total_produtos': total_produtos,
            'total_clientes': total_clientes,
            'agendamentos_hoje': agendamentos_hoje,
            'estoque_baixo': estoque_baixo
        }

    finally:
        conn.close()

@app.route('/api/alertas')
def api_alertas():
    """
    API para obter alertas do sistema

    Returns:
        Response: JSON com lista de alertas ativos
    """
    try:
        alertas = _gerar_alertas_sistema()
        return jsonify({'alertas': alertas})

    except Exception as e:
        return jsonify({'error': f'Erro ao obter alertas: {str(e)}'}), 500


def _gerar_alertas_sistema():
    """
    Gera lista de alertas baseados no estado atual do sistema

    Returns:
        list: Lista de alertas ativos
    """
    alertas = []

    # Verificar estoque baixo
    alertas.extend(_verificar_alertas_estoque())

    # Verificar agendamentos de hoje
    alertas.extend(_verificar_alertas_agendamentos())

    return alertas


def _verificar_alertas_estoque():
    """
    Verifica alertas relacionados ao estoque

    Returns:
        list: Lista de alertas de estoque
    """
    conn = db.get_connection()
    cursor = conn.cursor()
    alertas = []

    try:
        cursor.execute('''
            SELECT COUNT(*) as total FROM produtos
            WHERE estoque_atual <= estoque_minimo AND ativo = 1
        ''')
        estoque_baixo = cursor.fetchone()['total']

        if estoque_baixo > 0:
            alertas.append({
                'tipo': 'estoque',
                'titulo': 'Produtos com Estoque Baixo',
                'mensagem': f'{estoque_baixo} produto(s) estão com estoque abaixo do mínimo',
                'icone': 'exclamation-triangle',
                'cor': 'warning',
                'acao': 'Ver Relatório',
                'link': '/relatorios/estoque-baixo'
            })

    finally:
        conn.close()

    return alertas


def _verificar_alertas_agendamentos():
    """
    Verifica alertas relacionados aos agendamentos

    Returns:
        list: Lista de alertas de agendamentos
    """
    conn = db.get_connection()
    cursor = conn.cursor()
    alertas = []

    try:
        hoje = datetime.now().strftime('%Y-%m-%d')
        cursor.execute('''
            SELECT COUNT(*) as total FROM agendamentos
            WHERE DATE(data_agendamento) = ? AND status IN ('agendado', 'confirmado')
        ''', (hoje,))
        agendamentos_hoje = cursor.fetchone()['total']

        if agendamentos_hoje > 0:
            alertas.append({
                'tipo': 'agendamento',
                'titulo': 'Agendamentos Hoje',
                'mensagem': f'{agendamentos_hoje} agendamento(s) para hoje',
                'icone': 'calendar-check',
                'cor': 'info',
                'acao': 'Ver Agendamentos',
                'link': '/agendamentos'
            })

    finally:
        conn.close()

    return alertas

# ============================================================================
# ROTAS DE BACKUP E ADMINISTRAÇÃO
# ============================================================================

@app.route('/admin/backup')
@admin_required
def backup_dashboard():
    """
    Dashboard de backup do sistema (apenas administradores)

    Returns:
        str: Template do dashboard de backup
    """
    backup_system = BackupSystem()
    backups = backup_system.list_backups()

    return render_template('admin/backup.html', backups=backups)


@app.route('/admin/backup/create', methods=['POST'])
def create_backup():
    """
    Cria novo backup do sistema

    Returns:
        Response: JSON com resultado da operação
    """
    if session.get('user_tipo') != 'admin':
        return jsonify({'error': 'Acesso negado'}), 403

    try:
        backup_type = request.form.get('type', 'full')

        backup_system = BackupSystem()
        backup_path = backup_system.create_backup(backup_type)

        return jsonify({
            'success': True,
            'message': 'Backup criado com sucesso!',
            'backup_path': backup_path
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Erro ao criar backup: {str(e)}'
        }), 500


@app.route('/admin/backup/download/<filename>')
@admin_required
def download_backup(filename):
    """
    Download de arquivo de backup

    Args:
        filename (str): Nome do arquivo de backup

    Returns:
        Response: Arquivo de backup
    """
    backup_dir = 'backups'
    return send_from_directory(backup_dir, filename, as_attachment=True)

# ============================================================================
# FUNÇÃO PRINCIPAL - INICIALIZAÇÃO DO SISTEMA
# ============================================================================

def inicializar_sistema():
    """
    Inicializa o sistema Saúde Flex com todas as configurações necessárias
    """
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando sistema...")

    try:
        print("✅ Sistema inicializado com sucesso")
        print("🌐 Iniciando servidor Flask...")
        print(f"📍 Acesse: http://localhost:{AppConfig.PORT}")
        print("🔑 Login padrão: <EMAIL> / admin123")
        print("-" * 50)

        # Abrir navegador automaticamente após inicialização
        _configurar_abertura_automatica_navegador()

        # Iniciar servidor Flask
        app.run(
            debug=False,
            host=AppConfig.HOST,
            port=AppConfig.PORT,
            threaded=True
        )

    except Exception as e:
        _tratar_erro_inicializacao(e)


def _configurar_abertura_automatica_navegador():
    """
    Configura abertura automática do navegador após inicialização
    """
    import webbrowser
    import threading
    import time

    def abrir_navegador():
        """Abre o navegador após delay para garantir que o servidor esteja rodando"""
        time.sleep(3)  # Aguarda 3 segundos para o servidor inicializar
        try:
            webbrowser.open(f'http://localhost:{AppConfig.PORT}')
        except Exception as e:
            print(f"⚠️  Não foi possível abrir o navegador automaticamente: {e}")

    # Executar em thread separada para não bloquear o servidor
    threading.Thread(target=abrir_navegador, daemon=True).start()


def _tratar_erro_inicializacao(erro):
    """
    Trata erros durante a inicialização do sistema

    Args:
        erro (Exception): Exceção capturada durante inicialização
    """
    print(f"❌ Erro ao iniciar o sistema: {erro}")

    # Exibir traceback completo para debug
    import traceback
    traceback.print_exc()

    print("\n" + "="*50)
    print("💡 Dicas para resolver problemas:")
    print("1. Verifique se a porta não está em uso")
    print("2. Execute como administrador se necessário")
    print("3. Verifique as permissões de arquivo")
    print("="*50)

    input("Pressione Enter para sair...")


# Executar apenas se for o arquivo principal
if __name__ == '__main__':
    inicializar_sistema()
