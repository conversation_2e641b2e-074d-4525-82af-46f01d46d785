<!-- Navbar <PERSON>ão para todos os templates -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container">
        <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
            <i class="fas fa-heartbeat me-2"></i>Saúde Flex
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'index' else '' }}" href="{{ url_for('index') }}">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint in ['produtos', 'produto_novo'] else '' }}" href="{{ url_for('produtos') }}">
                        <i class="fas fa-box me-1"></i>Produtos
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint in ['clientes', 'cliente_novo'] else '' }}" href="{{ url_for('clientes') }}">
                        <i class="fas fa-users me-1"></i>Clientes
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint in ['vendas', 'venda_nova', 'venda_sucesso'] else '' }}" href="{{ url_for('vendas') }}">
                        <i class="fas fa-shopping-cart me-1"></i>Vendas
                    </a>
                </li>
                {% if session.user_tipo in ['admin', 'gerente'] %}
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint in ['categorias', 'categoria_nova', 'categoria_editar'] else '' }}" href="{{ url_for('categorias') }}">
                        <i class="fas fa-tags me-1"></i>Categorias
                    </a>
                </li>
                {% endif %}
                {% if session.user_tipo in ['admin', 'gerente'] %}
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint == 'relatorios' else '' }}" href="{{ url_for('relatorios') }}">
                        <i class="fas fa-chart-bar me-1"></i>Relatórios
                    </a>
                </li>
                {% endif %}
                {% if session.user_tipo == 'admin' %}
                <li class="nav-item">
                    <a class="nav-link {{ 'active' if request.endpoint in ['usuarios', 'usuario_novo'] else '' }}" href="{{ url_for('usuarios') }}">
                        <i class="fas fa-user-cog me-1"></i>Usuários
                    </a>
                </li>
                {% endif %}
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>{{ session.user_nome or 'Usuário' }}
                        <span class="badge bg-light text-dark ms-1">{{ session.user_tipo|title }}</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><h6 class="dropdown-header">{{ session.user_nome }}</h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-1"></i>Sair
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>
