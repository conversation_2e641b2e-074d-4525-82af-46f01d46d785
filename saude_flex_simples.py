#!/usr/bin/env python3
"""
Saúde Flex - Sistema Simples e Funcional
"""

from flask import Flask, render_template_string, request, redirect, url_for, session, flash, jsonify
from datetime import datetime
import sqlite3
import hashlib
import webbrowser
import threading
import time

# Configurações
DATABASE = 'saude_flex_simples.db'
SECRET_KEY = 'saude_flex_secret_key_2024'
PORT = 5000

# Criar app Flask
app = Flask(__name__)
app.secret_key = SECRET_KEY

def hash_password(password):
    """Hash simples da senha"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """Verificar senha"""
    return hashlib.sha256(password.encode()).hexdigest() == hashed

def init_database():
    """Inicializar banco de dados"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Tabela de usuários
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS usuarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            senha TEXT NOT NULL,
            tipo TEXT NOT NULL DEFAULT 'admin'
        )
    ''')
    
    # Tabela de produtos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS produtos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            preco_custo REAL NOT NULL,
            preco_venda REAL NOT NULL,
            estoque_atual INTEGER DEFAULT 0
        )
    ''')
    
    # Tabela de clientes
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clientes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT,
            telefone TEXT,
            cidade TEXT
        )
    ''')
    
    # Tabela de agendamentos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS agendamentos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            produto_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            data_agendamento DATETIME NOT NULL,
            status TEXT DEFAULT 'agendado',
            observacoes TEXT,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )
    ''')

    # Tabela de vendas
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            total_final REAL NOT NULL,
            data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Verificar se já existe um admin
    cursor.execute("SELECT id FROM usuarios WHERE tipo = 'admin' LIMIT 1")
    if not cursor.fetchone():
        senha_hash = hash_password('admin123')
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', ('Administrador', '<EMAIL>', senha_hash, 'admin'))
        print("✅ Usuário admin criado: <EMAIL> / admin123")
    
    # Criar dados de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM produtos")
    if cursor.fetchone()['total'] == 0:
        produtos_exemplo = [
            ('Whey Protein 1kg', 'Proteína em pó sabor chocolate', 50.00, 89.90, 20),
            ('Creatina 300g', 'Creatina monohidratada pura', 30.00, 59.90, 15),
            ('Shampoo Hidratante', 'Shampoo para cabelos secos', 15.00, 29.90, 25),
            ('Halteres 5kg', 'Par de halteres emborrachados', 80.00, 149.90, 8)
        ]
        
        for produto in produtos_exemplo:
            cursor.execute('''
                INSERT INTO produtos (nome, descricao, preco_custo, preco_venda, estoque_atual)
                VALUES (?, ?, ?, ?, ?)
            ''', produto)
        
        print("✅ Produtos de exemplo criados")
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes")
    if cursor.fetchone()['total'] == 0:
        clientes_exemplo = [
            ('João Silva', '<EMAIL>', '(11) 99999-9999', 'São Paulo'),
            ('Maria Santos', '<EMAIL>', '(11) 88888-8888', 'Rio de Janeiro'),
            ('Pedro Costa', '<EMAIL>', '(11) 77777-7777', 'Belo Horizonte')
        ]
        
        for cliente in clientes_exemplo:
            cursor.execute('''
                INSERT INTO clientes (nome, email, telefone, cidade)
                VALUES (?, ?, ?, ?)
            ''', cliente)
        
        print("✅ Clientes de exemplo criados")
    
    # Criar vendas de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM vendas")
    if cursor.fetchone()['total'] == 0:
        vendas_exemplo = [
            (1, 1, 149.80),  # João comprou
            (2, 1, 179.70),  # Maria comprou
            (3, 1, 89.90)    # Pedro comprou
        ]
        
        for venda in vendas_exemplo:
            cursor.execute('''
                INSERT INTO vendas (cliente_id, vendedor_id, total_final)
                VALUES (?, ?, ?)
            ''', venda)
        
        print("✅ Vendas de exemplo criadas")

    # Criar agendamentos de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM agendamentos")
    if cursor.fetchone()['total'] == 0:
        agendamentos_exemplo = [
            (1, 1, 1, '2025-06-16 09:00:00', 'agendado', 'Primeira consulta'),
            (2, 2, 1, '2025-06-16 14:00:00', 'agendado', 'Avaliação nutricional'),
            (3, 3, 1, '2025-06-17 10:30:00', 'agendado', 'Acompanhamento'),
            (1, 4, 1, '2025-06-17 16:00:00', 'confirmado', 'Treino personalizado'),
            (2, 1, 1, '2025-06-18 08:00:00', 'agendado', 'Consulta de retorno'),
            (3, 2, 1, '2025-06-18 15:30:00', 'agendado', 'Orientação alimentar'),
            (1, 3, 1, '2025-06-19 11:00:00', 'agendado', 'Avaliação de progresso'),
            (2, 4, 1, '2025-06-20 09:30:00', 'agendado', 'Sessão de exercícios')
        ]

        for agendamento in agendamentos_exemplo:
            cursor.execute('''
                INSERT INTO agendamentos (cliente_id, produto_id, vendedor_id, data_agendamento, status, observacoes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', agendamento)

        print("✅ Agendamentos de exemplo criados")
    
    conn.commit()
    conn.close()

def get_db():
    """Obter conexão com banco"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def login_required(f):
    """Decorator para verificar login"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Rotas
@app.route('/')
@login_required
def index():
    """Dashboard principal"""
    conn = get_db()
    cursor = conn.cursor()

    # Estatísticas básicas
    cursor.execute("SELECT COUNT(*) as total FROM produtos")
    total_produtos = cursor.fetchone()['total']

    cursor.execute("SELECT COUNT(*) as total FROM clientes")
    total_clientes = cursor.fetchone()['total']

    cursor.execute("SELECT COUNT(*) as total FROM vendas")
    total_vendas = cursor.fetchone()['total']

    cursor.execute("SELECT COALESCE(SUM(total_final), 0) as total FROM vendas")
    faturamento_total = cursor.fetchone()['total']

    # Agendamentos
    cursor.execute("SELECT COUNT(*) as total FROM agendamentos WHERE DATE(data_agendamento) = DATE('now')")
    agendamentos_hoje = cursor.fetchone()['total']

    cursor.execute("SELECT COUNT(*) as total FROM agendamentos WHERE DATE(data_agendamento) >= DATE('now')")
    agendamentos_futuros = cursor.fetchone()['total']

    # Vendas por mês (últimos 6 meses)
    cursor.execute('''
        SELECT strftime('%Y-%m', data_venda) as mes,
               COUNT(*) as quantidade,
               SUM(total_final) as valor
        FROM vendas
        WHERE data_venda >= date('now', '-6 months')
        GROUP BY strftime('%Y-%m', data_venda)
        ORDER BY mes
    ''')
    vendas_por_mes = [dict(row) for row in cursor.fetchall()]

    # Produtos mais vendidos
    cursor.execute('''
        SELECT p.nome, COUNT(*) as vendas
        FROM vendas v
        JOIN produtos p ON v.cliente_id = p.id  -- Simulação, seria melhor com tabela de itens
        GROUP BY p.nome
        ORDER BY vendas DESC
        LIMIT 5
    ''')
    produtos_populares = [dict(row) for row in cursor.fetchall()]

    # Status dos agendamentos
    cursor.execute('''
        SELECT status, COUNT(*) as quantidade
        FROM agendamentos
        WHERE DATE(data_agendamento) >= DATE('now')
        GROUP BY status
    ''')
    status_agendamentos = [dict(row) for row in cursor.fetchall()]

    # Agendamentos próximos
    cursor.execute('''
        SELECT a.*, c.nome as cliente_nome, p.nome as produto_nome, u.nome as vendedor_nome
        FROM agendamentos a
        JOIN clientes c ON a.cliente_id = c.id
        JOIN produtos p ON a.produto_id = p.id
        JOIN usuarios u ON a.vendedor_id = u.id
        WHERE DATE(a.data_agendamento) >= DATE('now')
        ORDER BY a.data_agendamento
        LIMIT 10
    ''')
    agendamentos_proximos = [dict(row) for row in cursor.fetchall()]

    # Vendas recentes
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
        FROM vendas v
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        ORDER BY v.data_venda DESC
        LIMIT 5
    ''')
    vendas_recentes = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template_string(TEMPLATE_DASHBOARD,
                                total_produtos=total_produtos,
                                total_clientes=total_clientes,
                                total_vendas=total_vendas,
                                faturamento_total=faturamento_total,
                                agendamentos_hoje=agendamentos_hoje,
                                agendamentos_futuros=agendamentos_futuros,
                                vendas_por_mes=vendas_por_mes,
                                produtos_populares=produtos_populares,
                                status_agendamentos=status_agendamentos,
                                agendamentos_proximos=agendamentos_proximos,
                                vendas_recentes=vendas_recentes)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        email = request.form['email']
        senha = request.form['senha']
        
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM usuarios WHERE email = ?', (email,))
        usuario = cursor.fetchone()
        conn.close()
        
        if usuario and verify_password(senha, usuario['senha']):
            session['user_id'] = usuario['id']
            session['user_nome'] = usuario['nome']
            session['user_tipo'] = usuario['tipo']
            session['user_email'] = usuario['email']
            
            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Email ou senha incorretos!', 'error')
    
    return render_template_string(TEMPLATE_LOGIN)

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    flash('Logout realizado com sucesso!', 'success')
    return redirect(url_for('login'))

@app.route('/produtos')
@login_required
def produtos():
    """Lista de produtos"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM produtos ORDER BY nome')
    produtos_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template_string(TEMPLATE_PRODUTOS, produtos=produtos_lista)

@app.route('/clientes')
@login_required
def clientes():
    """Lista de clientes"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM clientes ORDER BY nome')
    clientes_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template_string(TEMPLATE_CLIENTES, clientes=clientes_lista)

@app.route('/vendas')
@login_required
def vendas():
    """Lista de vendas"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
        FROM vendas v
        JOIN clientes c ON v.cliente_id = c.id
        JOIN usuarios u ON v.vendedor_id = u.id
        ORDER BY v.data_venda DESC
    ''')
    vendas_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template_string(TEMPLATE_VENDAS, vendas=vendas_lista)

@app.route('/api/agendamentos')
@login_required
def api_agendamentos():
    """API para fornecer agendamentos em formato JSON para o calendário"""
    conn = get_db()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT a.*, c.nome as cliente_nome, p.nome as produto_nome, u.nome as vendedor_nome
        FROM agendamentos a
        JOIN clientes c ON a.cliente_id = c.id
        JOIN produtos p ON a.produto_id = p.id
        JOIN usuarios u ON a.vendedor_id = u.id
        ORDER BY a.data_agendamento
    ''')

    agendamentos = []
    for row in cursor.fetchall():
        agendamento = dict(row)
        agendamentos.append({
            'id': agendamento['id'],
            'title': f"{agendamento['cliente_nome']} - {agendamento['produto_nome']}",
            'start': agendamento['data_agendamento'],
            'backgroundColor': '#28a745' if agendamento['status'] == 'confirmado' else '#007bff',
            'borderColor': '#28a745' if agendamento['status'] == 'confirmado' else '#007bff',
            'extendedProps': {
                'cliente': agendamento['cliente_nome'],
                'produto': agendamento['produto_nome'],
                'vendedor': agendamento['vendedor_nome'],
                'status': agendamento['status'],
                'observacoes': agendamento['observacoes']
            }
        })

    conn.close()
    return jsonify(agendamentos)

def abrir_navegador():
    """Abrir navegador automaticamente"""
    time.sleep(3)
    webbrowser.open(f'http://localhost:{PORT}')

# Templates inline
TEMPLATE_LOGIN = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-heartbeat fa-3x text-primary"></i>
                            <h3 class="mt-2">Saúde Flex</h3>
                            <p class="text-muted">Sistema de Agendamentos e Vendas</p>
                        </div>
                        
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="senha" class="form-label">Senha</label>
                                <input type="password" class="form-control" id="senha" name="senha" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-sign-in-alt me-2"></i>Entrar
                            </button>
                        </form>
                        
                        <div class="mt-4 text-center">
                            <small class="text-muted">
                                <strong>Login padrão:</strong><br>
                                Email: <EMAIL><br>
                                Senha: admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_DASHBOARD = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    <style>
        .dashboard-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            color: white;
            transition: transform 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .dashboard-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .dashboard-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .dashboard-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .chart-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .calendar-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .fc-event {
            border-radius: 8px !important;
            border: none !important;
            padding: 2px 6px !important;
        }
        .stats-icon {
            font-size: 3rem;
            opacity: 0.8;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .content-wrapper {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        .modern-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            transition: transform 0.3s ease;
        }
        .modern-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('produtos') }}">
                    <i class="fas fa-box me-1"></i>Produtos
                </a>
                <a class="nav-link" href="{{ url_for('clientes') }}">
                    <i class="fas fa-users me-1"></i>Clientes
                </a>
                <a class="nav-link" href="{{ url_for('vendas') }}">
                    <i class="fas fa-shopping-cart me-1"></i>Vendas
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="gradient-bg">
        <div class="content-wrapper">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h1 class="display-6 fw-bold mb-0">
                        <i class="fas fa-tachometer-alt me-3" style="color: #667eea;"></i>
                        Dashboard Saúde Flex
                    </h1>
                    <p class="text-muted">Visão geral do seu negócio em tempo real</p>
                </div>
            </div>

            <!-- Cards de Estatísticas Modernas -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card dashboard-card h-100">
                        <div class="card-body d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="text-uppercase fw-bold mb-1" style="font-size: 0.8rem;">
                                    Total de Produtos
                                </div>
                                <div class="h2 mb-0 fw-bold">
                                    {{ total_produtos }}
                                </div>
                                <small class="opacity-75">Produtos cadastrados</small>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-box stats-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card dashboard-card success h-100">
                        <div class="card-body d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="text-uppercase fw-bold mb-1" style="font-size: 0.8rem;">
                                    Total de Clientes
                                </div>
                                <div class="h2 mb-0 fw-bold">
                                    {{ total_clientes }}
                                </div>
                                <small class="opacity-75">Clientes ativos</small>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-users stats-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card dashboard-card info h-100">
                        <div class="card-body d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="text-uppercase fw-bold mb-1" style="font-size: 0.8rem;">
                                    Agendamentos Hoje
                                </div>
                                <div class="h2 mb-0 fw-bold">
                                    {{ agendamentos_hoje }}
                                </div>
                                <small class="opacity-75">Próximos: {{ agendamentos_futuros }}</small>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-calendar-check stats-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card dashboard-card warning h-100">
                        <div class="card-body d-flex align-items-center">
                            <div class="flex-grow-1">
                                <div class="text-uppercase fw-bold mb-1" style="font-size: 0.8rem;">
                                    Faturamento Total
                                </div>
                                <div class="h2 mb-0 fw-bold">
                                    R$ {{ "%.0f"|format(faturamento_total) }}
                                </div>
                                <small class="opacity-75">{{ total_vendas }} vendas</small>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-dollar-sign stats-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gráficos e Calendário -->
            <div class="row mb-4">
                <!-- Gráfico de Vendas -->
                <div class="col-lg-6 mb-4">
                    <div class="chart-container">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-chart-line me-2 text-primary"></i>
                            Vendas por Mês
                        </h5>
                        <canvas id="vendasChart" height="300"></canvas>
                    </div>
                </div>

                <!-- Gráfico de Status dos Agendamentos -->
                <div class="col-lg-6 mb-4">
                    <div class="chart-container">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-chart-pie me-2 text-success"></i>
                            Status dos Agendamentos
                        </h5>
                        <canvas id="statusChart" height="300"></canvas>
                    </div>
                </div>
            </div>

            <!-- Calendário de Agendamentos -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="calendar-container">
                        <h5 class="fw-bold mb-3">
                            <i class="fas fa-calendar-alt me-2 text-info"></i>
                            Calendário de Agendamentos
                        </h5>
                        <div id="calendar"></div>
                    </div>
                </div>
            </div>

            <!-- Informações Recentes -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="card-header bg-transparent border-0 pb-0">
                            <h6 class="fw-bold text-primary mb-0">
                                <i class="fas fa-clock me-2"></i>Agendamentos Próximos
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if agendamentos_proximos %}
                                {% for agendamento in agendamentos_proximos[:5] %}
                                <div class="d-flex align-items-center mb-3 p-2 rounded" style="background: #f8f9fa;">
                                    <div class="me-3">
                                        <div class="rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px; background: #007bff; color: white;">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">{{ agendamento.cliente_nome }}</div>
                                        <small class="text-muted">{{ agendamento.produto_nome }}</small>
                                        <div class="small text-primary">{{ agendamento.data_agendamento }}</div>
                                    </div>
                                    <div>
                                        <span class="badge bg-{{ 'success' if agendamento.status == 'confirmado' else 'primary' }}">
                                            {{ agendamento.status.title() }}
                                        </span>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-calendar-plus fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">Nenhum agendamento próximo</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="modern-card">
                        <div class="card-header bg-transparent border-0 pb-0">
                            <h6 class="fw-bold text-success mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>Vendas Recentes
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if vendas_recentes %}
                                {% for venda in vendas_recentes %}
                                <div class="d-flex align-items-center mb-3 p-2 rounded" style="background: #f8f9fa;">
                                    <div class="me-3">
                                        <div class="rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px; background: #28a745; color: white;">
                                            <i class="fas fa-dollar-sign"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">{{ venda.cliente_nome }}</div>
                                        <small class="text-muted">Vendedor: {{ venda.vendedor_nome }}</small>
                                        <div class="small text-success">{{ venda.data_venda }}</div>
                                    </div>
                                    <div>
                                        <span class="fw-bold text-success">R$ {{ "%.2f"|format(venda.total_final) }}</span>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-chart-line fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">Nenhuma venda recente</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Configuração dos gráficos
        document.addEventListener('DOMContentLoaded', function() {

            // Gráfico de Vendas por Mês
            const vendasCtx = document.getElementById('vendasChart').getContext('2d');
            const vendasData = {{ vendas_por_mes | tojson }};

            const meses = vendasData.map(item => {
                const [ano, mes] = item.mes.split('-');
                const data = new Date(ano, mes - 1);
                return data.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
            });
            const valores = vendasData.map(item => item.valor || 0);

            new Chart(vendasCtx, {
                type: 'line',
                data: {
                    labels: meses,
                    datasets: [{
                        label: 'Faturamento (R$)',
                        data: valores,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#667eea',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + value.toFixed(0);
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Gráfico de Status dos Agendamentos
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            const statusData = {{ status_agendamentos | tojson }};

            const statusLabels = statusData.map(item => item.status.charAt(0).toUpperCase() + item.status.slice(1));
            const statusQuantidades = statusData.map(item => item.quantidade);

            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: statusLabels,
                    datasets: [{
                        data: statusQuantidades,
                        backgroundColor: [
                            '#667eea',
                            '#28a745',
                            '#ffc107',
                            '#dc3545'
                        ],
                        borderWidth: 0,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });

            // Configuração do Calendário
            const calendarEl = document.getElementById('calendar');
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                locale: 'pt-br',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                buttonText: {
                    today: 'Hoje',
                    month: 'Mês',
                    week: 'Semana',
                    day: 'Dia'
                },
                height: 600,
                events: '/api/agendamentos',
                eventClick: function(info) {
                    const props = info.event.extendedProps;
                    alert(`Cliente: ${props.cliente}\\nProduto: ${props.produto}\\nVendedor: ${props.vendedor}\\nStatus: ${props.status}\\nObservações: ${props.observacoes || 'Nenhuma'}`);
                },
                eventMouseEnter: function(info) {
                    info.el.style.cursor = 'pointer';
                },
                dayMaxEvents: 3,
                moreLinkClick: 'popover'
            });

            calendar.render();
        });
    </script>
</body>
</html>
'''

TEMPLATE_PRODUTOS = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('clientes') }}">
                    <i class="fas fa-users me-1"></i>Clientes
                </a>
                <a class="nav-link" href="{{ url_for('vendas') }}">
                    <i class="fas fa-shopping-cart me-1"></i>Vendas
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-box me-2 text-primary"></i>
                    Produtos
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold text-primary">Lista de Produtos</h6>
                    </div>
                    <div class="card-body">
                        {% if produtos %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Descrição</th>
                                            <th>Preço Custo</th>
                                            <th>Preço Venda</th>
                                            <th>Estoque</th>
                                            <th>Margem</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for produto in produtos %}
                                        <tr>
                                            <td><strong>{{ produto.nome }}</strong></td>
                                            <td>{{ produto.descricao or '-' }}</td>
                                            <td>R$ {{ "%.2f"|format(produto.preco_custo) }}</td>
                                            <td><strong>R$ {{ "%.2f"|format(produto.preco_venda) }}</strong></td>
                                            <td>
                                                <span class="badge bg-{{ 'danger' if produto.estoque_atual <= 5 else 'success' }}">
                                                    {{ produto.estoque_atual }}
                                                </span>
                                            </td>
                                            <td>
                                                {% set margem = ((produto.preco_venda - produto.preco_custo) / produto.preco_custo * 100) %}
                                                <span class="badge bg-info">{{ "%.1f"|format(margem) }}%</span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <h5>Nenhum produto cadastrado</h5>
                                <p class="text-muted">Cadastre produtos para começar a usar o sistema.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_CLIENTES = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clientes - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('produtos') }}">
                    <i class="fas fa-box me-1"></i>Produtos
                </a>
                <a class="nav-link" href="{{ url_for('vendas') }}">
                    <i class="fas fa-shopping-cart me-1"></i>Vendas
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-users me-2 text-primary"></i>
                    Clientes
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold text-primary">Lista de Clientes</h6>
                    </div>
                    <div class="card-body">
                        {% if clientes %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Email</th>
                                            <th>Telefone</th>
                                            <th>Cidade</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for cliente in clientes %}
                                        <tr>
                                            <td><strong>{{ cliente.nome }}</strong></td>
                                            <td>{{ cliente.email or '-' }}</td>
                                            <td>{{ cliente.telefone or '-' }}</td>
                                            <td>{{ cliente.cidade or '-' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5>Nenhum cliente cadastrado</h5>
                                <p class="text-muted">Cadastre clientes para começar a usar o sistema.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

TEMPLATE_VENDAS = '''
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendas - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('produtos') }}">
                    <i class="fas fa-box me-1"></i>Produtos
                </a>
                <a class="nav-link" href="{{ url_for('clientes') }}">
                    <i class="fas fa-users me-1"></i>Clientes
                </a>
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>{{ session.user_nome }}
                </span>
                <a class="nav-link" href="{{ url_for('logout') }}">
                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">
                    <i class="fas fa-shopping-cart me-2 text-primary"></i>
                    Vendas
                </h1>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 fw-bold text-primary">Lista de Vendas</h6>
                    </div>
                    <div class="card-body">
                        {% if vendas %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Data</th>
                                            <th>Cliente</th>
                                            <th>Vendedor</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for venda in vendas %}
                                        <tr>
                                            <td><strong>#{{ venda.id }}</strong></td>
                                            <td>{{ venda.data_venda }}</td>
                                            <td>{{ venda.cliente_nome }}</td>
                                            <td>{{ venda.vendedor_nome }}</td>
                                            <td><strong class="text-success">R$ {{ "%.2f"|format(venda.total_final) }}</strong></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h5>Nenhuma venda realizada</h5>
                                <p class="text-muted">As vendas aparecerão aqui quando forem realizadas.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

if __name__ == '__main__':
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando banco de dados...")

    try:
        init_database()
        print("✅ Banco de dados inicializado")
        print("🌐 Iniciando servidor Flask...")
        print(f"📍 Acesse: http://localhost:{PORT}")
        print("🔑 Login: <EMAIL> / admin123")
        print("-" * 50)

        # Abrir navegador automaticamente
        threading.Thread(target=abrir_navegador, daemon=True).start()

        app.run(debug=False, host='127.0.0.1', port=PORT)

    except Exception as e:
        print(f"❌ Erro ao iniciar: {e}")
        import traceback
        traceback.print_exc()
        input("Pressione Enter para sair...")
