/**
 * Sistema de Gestão Saúde Flex
 * Arquivo principal de JavaScript com funcionalidades globais
 *
 * Este arquivo contém:
 * - Configurações globais do sistema
 * - Utilitários para formatação e validação
 * - Sistema de notificações
 * - Controles de loading
 * - Sistema de modais
 * - Cliente para APIs
 *
 * <AUTHOR> Saúde Flex
 * @version 1.0.0
 */

// Namespace principal do sistema
const SaudeFlex = {
    config: {
        apiUrl: '/api',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: 'HH:mm',
        currency: 'BRL'
    },
    
    // Utilitários
    utils: {
        // Formatar moeda
        formatCurrency: function(value) {
            return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(value);
        },
        
        // Formatar data
        formatDate: function(date) {
            return new Date(date).toLocaleDateString('pt-BR');
        },
        
        // Formatar data e hora
        formatDateTime: function(date) {
            return new Date(date).toLocaleString('pt-BR');
        },
        
        // Validar email
        validateEmail: function(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },
        
        // Validar telefone
        validatePhone: function(phone) {
            const re = /^\(\d{2}\)\s\d{4,5}-\d{4}$/;
            return re.test(phone);
        },
        
        // Máscara para telefone
        phoneMask: function(value) {
            value = value.replace(/\D/g, '');
            value = value.replace(/^(\d{2})(\d)/g, '($1) $2');
            value = value.replace(/(\d)(\d{4})$/, '$1-$2');
            return value;
        },
        
        // Máscara para CEP
        cepMask: function(value) {
            value = value.replace(/\D/g, '');
            value = value.replace(/^(\d{5})(\d)/, '$1-$2');
            return value;
        },
        
        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    },
    
    // Notificações
    notify: {
        success: function(message, options = {}) {
            this.show(message, 'success', options);
        },

        error: function(message, options = {}) {
            this.show(message, 'error', options);
        },

        warning: function(message, options = {}) {
            this.show(message, 'warning', options);
        },

        info: function(message, options = {}) {
            this.show(message, 'info', options);
        },

        show: function(message, type = 'info', options = {}) {
            const alertClass = type === 'error' ? 'danger' : type;
            const icon = {
                success: 'check-circle',
                error: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            }[type];

            const autoClose = options.autoClose !== false;
            const duration = options.duration || 5000;
            const position = options.position || 'top';

            const alertId = 'alert-' + Date.now();
            const alertHtml = `
                <div id="${alertId}" class="alert alert-${alertClass} alert-dismissible fade show notification-alert" role="alert" style="position: relative; z-index: 1050;">
                    <i class="fas fa-${icon} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // Container para notificações
            let notificationContainer = document.getElementById('notification-container');
            if (!notificationContainer) {
                notificationContainer = document.createElement('div');
                notificationContainer.id = 'notification-container';
                notificationContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1060;
                    max-width: 400px;
                `;
                document.body.appendChild(notificationContainer);
            }

            // Adicionar nova notificação
            notificationContainer.insertAdjacentHTML('beforeend', alertHtml);

            // Auto-remover se habilitado
            if (autoClose) {
                setTimeout(() => {
                    const alert = document.getElementById(alertId);
                    if (alert) {
                        alert.classList.remove('show');
                        setTimeout(() => alert.remove(), 150);
                    }
                }, duration);
            }

            // Limitar número de notificações
            const alerts = notificationContainer.querySelectorAll('.alert');
            if (alerts.length > 5) {
                alerts[0].remove();
            }
        },

        // Notificação de progresso
        progress: function(message, percentage) {
            const progressId = 'progress-notification';
            let progressAlert = document.getElementById(progressId);

            if (!progressAlert) {
                const progressHtml = `
                    <div id="${progressId}" class="alert alert-info alert-dismissible fade show notification-alert" role="alert">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        <span id="progress-message">${message}</span>
                        <div class="progress mt-2" style="height: 5px;">
                            <div id="progress-bar" class="progress-bar" role="progressbar" style="width: ${percentage}%"></div>
                        </div>
                    </div>
                `;

                let container = document.getElementById('notification-container');
                if (!container) {
                    container = document.createElement('div');
                    container.id = 'notification-container';
                    container.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        z-index: 1060;
                        max-width: 400px;
                    `;
                    document.body.appendChild(container);
                }

                container.insertAdjacentHTML('beforeend', progressHtml);
            } else {
                document.getElementById('progress-message').textContent = message;
                document.getElementById('progress-bar').style.width = percentage + '%';
            }

            // Remover quando completo
            if (percentage >= 100) {
                setTimeout(() => {
                    const alert = document.getElementById(progressId);
                    if (alert) {
                        alert.remove();
                    }
                }, 1000);
            }
        }
    },
    
    // Loading
    loading: {
        show: function(element) {
            if (typeof element === 'string') {
                element = document.querySelector(element);
            }
            
            if (element) {
                element.disabled = true;
                const originalText = element.innerHTML;
                element.setAttribute('data-original-text', originalText);
                element.innerHTML = `
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    Carregando...
                `;
            }
        },
        
        hide: function(element) {
            if (typeof element === 'string') {
                element = document.querySelector(element);
            }
            
            if (element) {
                element.disabled = false;
                const originalText = element.getAttribute('data-original-text');
                if (originalText) {
                    element.innerHTML = originalText;
                    element.removeAttribute('data-original-text');
                }
            }
        }
    },
    
    // Modal
    modal: {
        show: function(title, content, options = {}) {
            const modalId = 'dynamicModal';
            let modal = document.getElementById(modalId);
            
            if (!modal) {
                const modalHtml = `
                    <div class="modal fade" id="${modalId}" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title"></h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body"></div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                modal = document.getElementById(modalId);
            }
            
            modal.querySelector('.modal-title').textContent = title;
            modal.querySelector('.modal-body').innerHTML = content;
            
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();
            
            return bootstrapModal;
        },
        
        confirm: function(message, callback) {
            const modalHtml = `
                <div class="text-center">
                    <i class="fas fa-question-circle fa-3x text-warning mb-3"></i>
                    <p>${message}</p>
                </div>
            `;
            
            const modal = this.show('Confirmação', modalHtml);
            
            // Adicionar botões de confirmação
            const modalElement = document.getElementById('dynamicModal');
            const footer = modalElement.querySelector('.modal-footer');
            footer.innerHTML = `
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-danger" id="confirmBtn">Confirmar</button>
            `;
            
            document.getElementById('confirmBtn').addEventListener('click', function() {
                callback();
                modal.hide();
            });
        }
    },
    
    // API
    api: {
        get: async function(url) {
            try {
                const response = await fetch(this.config.apiUrl + url);
                return await response.json();
            } catch (error) {
                SaudeFlex.notify.error('Erro ao carregar dados: ' + error.message);
                throw error;
            }
        },
        
        post: async function(url, data) {
            try {
                const response = await fetch(this.config.apiUrl + url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            } catch (error) {
                SaudeFlex.notify.error('Erro ao enviar dados: ' + error.message);
                throw error;
            }
        }
    }
};

// Inicialização quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Aplicar máscaras automaticamente
    document.querySelectorAll('input[data-mask="phone"]').forEach(input => {
        input.addEventListener('input', function() {
            this.value = SaudeFlex.utils.phoneMask(this.value);
        });
    });
    
    document.querySelectorAll('input[data-mask="cep"]').forEach(input => {
        input.addEventListener('input', function() {
            this.value = SaudeFlex.utils.cepMask(this.value);
        });
    });
    
    // Validação de formulários
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const emailInputs = form.querySelectorAll('input[type="email"]');
            emailInputs.forEach(input => {
                if (input.value && !SaudeFlex.utils.validateEmail(input.value)) {
                    e.preventDefault();
                    SaudeFlex.notify.error('Por favor, insira um email válido.');
                    input.focus();
                    return false;
                }
            });
        });
    });
    
    // Auto-hide alerts
    setTimeout(() => {
        document.querySelectorAll('.alert').forEach(alert => {
            if (alert.classList.contains('show')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
    
    // Tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// Funções globais para compatibilidade
function showLoading(element) {
    SaudeFlex.loading.show(element);
}

function hideLoading(element) {
    SaudeFlex.loading.hide(element);
}

function showNotification(message, type = 'info') {
    SaudeFlex.notify[type](message);
}

function confirmAction(message, callback) {
    SaudeFlex.modal.confirm(message, callback);
}

// Exportar para uso global
window.SaudeFlex = SaudeFlex;
