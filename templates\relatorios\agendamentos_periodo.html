{% extends "base.html" %}

{% block title %}Relatório de Agendamentos por Período - Saúde Flex{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2 gradient-text">
                    <i class="fas fa-calendar-check me-2"></i>
                    Relatório de Agendamentos por Período
                </h1>
                <p class="text-muted mb-0">Análise detalhada dos agendamentos realizados</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ url_for('relatorios') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    Voltar
                </a>
                <button type="button" class="btn btn-primary" onclick="exportarRelatorio()">
                    <i class="fas fa-download me-2"></i>
                    Exportar
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    Filtros
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('relatorio_agendamentos_periodo') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="data_inicio" class="form-label">Data Início</label>
                            <input type="date" class="form-control" id="data_inicio" name="data_inicio" 
                                   value="{{ filtros.data_inicio or '' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="data_fim" class="form-label">Data Fim</label>
                            <input type="date" class="form-control" id="data_fim" name="data_fim" 
                                   value="{{ filtros.data_fim or '' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="vendedor_id" class="form-label">Vendedor</label>
                            <select class="form-select" id="vendedor_id" name="vendedor_id">
                                <option value="">Todos os vendedores</option>
                                {% for vendedor in vendedores %}
                                <option value="{{ vendedor.id }}" 
                                        {% if filtros.vendedor_id == vendedor.id|string %}selected{% endif %}>
                                    {{ vendedor.nome }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Todos os status</option>
                                <option value="agendado" {% if filtros.status == 'agendado' %}selected{% endif %}>Agendado</option>
                                <option value="confirmado" {% if filtros.status == 'confirmado' %}selected{% endif %}>Confirmado</option>
                                <option value="realizado" {% if filtros.status == 'realizado' %}selected{% endif %}>Realizado</option>
                                <option value="cancelado" {% if filtros.status == 'cancelado' %}selected{% endif %}>Cancelado</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                            <a href="{{ url_for('relatorio_agendamentos_periodo') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-eraser me-2"></i>
                                Limpar Filtros
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card hover-lift h-100">
            <div class="card-body d-flex align-items-center">
                <div class="stat-icon primary me-3">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number">{{ estatisticas.total_agendamentos }}</div>
                    <div class="stat-label">Total de Agendamentos</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card hover-lift h-100">
            <div class="card-body d-flex align-items-center">
                <div class="stat-icon warning me-3">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number">{{ estatisticas.agendamentos_por_status.agendado or 0 }}</div>
                    <div class="stat-label">Agendados</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card hover-lift h-100">
            <div class="card-body d-flex align-items-center">
                <div class="stat-icon info me-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number">{{ estatisticas.agendamentos_por_status.confirmado or 0 }}</div>
                    <div class="stat-label">Confirmados</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card hover-lift h-100">
            <div class="card-body d-flex align-items-center">
                <div class="stat-icon success me-3">
                    <i class="fas fa-check-double"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number">{{ estatisticas.agendamentos_por_status.realizado or 0 }}</div>
                    <div class="stat-label">Realizados</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Table -->
<div class="row">
    <div class="col-12">
        <div class="card hover-lift">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    Agendamentos Encontrados ({{ agendamentos|length }})
                </h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>
                        Exportar
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportarPDF()">
                            <i class="fas fa-file-pdf me-2"></i>PDF
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportarExcel()">
                            <i class="fas fa-file-excel me-2"></i>Excel
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportarCSV()">
                            <i class="fas fa-file-csv me-2"></i>CSV
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                {% if agendamentos %}
                <div class="table-responsive">
                    <table class="table table-hover" id="tabela-agendamentos">
                        <thead>
                            <tr>
                                <th>Data/Hora</th>
                                <th>Cliente</th>
                                <th>Produto</th>
                                <th>Vendedor</th>
                                <th>Status</th>
                                <th>Observações</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agendamento in agendamentos %}
                            <tr>
                                <td>
                                    <div class="fw-bold">{{ agendamento.data_agendamento_formatada }}</div>
                                    <small class="text-muted">{{ agendamento.data_agendamento.strftime('%H:%M') if agendamento.data_agendamento else '' }}</small>
                                </td>
                                <td>
                                    <div class="fw-bold">{{ agendamento.cliente_nome }}</div>
                                    <small class="text-muted">{{ agendamento.cliente_telefone }}</small>
                                </td>
                                <td>{{ agendamento.produto_nome }}</td>
                                <td>{{ agendamento.vendedor_nome }}</td>
                                <td>
                                    {% if agendamento.status == 'agendado' %}
                                        <span class="badge bg-warning">Agendado</span>
                                    {% elif agendamento.status == 'confirmado' %}
                                        <span class="badge bg-primary">Confirmado</span>
                                    {% elif agendamento.status == 'realizado' %}
                                        <span class="badge bg-success">Realizado</span>
                                    {% elif agendamento.status == 'cancelado' %}
                                        <span class="badge bg-danger">Cancelado</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ agendamento.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if agendamento.observacoes %}
                                        <span class="text-truncate d-inline-block" style="max-width: 150px;" 
                                              title="{{ agendamento.observacoes }}">
                                            {{ agendamento.observacoes }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('agendamento_detalhes', id=agendamento.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="Ver detalhes">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('agendamento_editar', id=agendamento.id) }}" 
                                           class="btn btn-sm btn-outline-warning" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhum agendamento encontrado</h5>
                    <p class="text-muted">Tente ajustar os filtros ou verificar se há agendamentos cadastrados.</p>
                    <a href="{{ url_for('agendamento_novo') }}" class="btn btn-primary">
                        <i class="fas fa-calendar-plus me-2"></i>
                        Criar Novo Agendamento
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Export functions
function exportarRelatorio() {
    SaudeFlex.notify.info('Funcionalidade de exportação será implementada em breve');
}

function exportarPDF() {
    SaudeFlex.notify.info('Exportação para PDF será implementada em breve');
}

function exportarExcel() {
    SaudeFlex.notify.info('Exportação para Excel será implementada em breve');
}

function exportarCSV() {
    SaudeFlex.notify.info('Exportação para CSV será implementada em breve');
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
