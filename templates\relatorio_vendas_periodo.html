<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Vendas por Período - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .resumo-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .btn-print {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            color: white;
        }
        .btn-print:hover {
            background: linear-gradient(135deg, #5a2d91 0%, #c02456 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-chart-line text-success me-2"></i>
                    Relatório de Vendas por Período
                </h1>
                <p class="text-muted">Análise detalhada do desempenho de vendas</p>
            </div>
            <div class="col-auto">
                <button class="btn btn-print me-2" onclick="window.print()">
                    <i class="fas fa-print me-1"></i>Imprimir
                </button>
                <a href="{{ url_for('relatorios') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar
                </a>
            </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="data_inicio" class="form-label">Data Início</label>
                        <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="{{ data_inicio }}">
                    </div>
                    <div class="col-md-3">
                        <label for="data_fim" class="form-label">Data Fim</label>
                        <input type="date" class="form-control" id="data_fim" name="data_fim" value="{{ data_fim }}">
                    </div>
                    <div class="col-md-4">
                        <label for="vendedor_id" class="form-label">Vendedor</label>
                        <select class="form-select" id="vendedor_id" name="vendedor_id">
                            <option value="">Todos os vendedores</option>
                            {% for vendedor in vendedores %}
                            <option value="{{ vendedor.id }}" {{ 'selected' if vendedor_id == vendedor.id|string else '' }}>
                                {{ vendedor.nome }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-filter me-1"></i>Filtrar
                        </button>
                        <a href="{{ url_for('relatorio_vendas_periodo') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Limpar
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Resumo -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card resumo-card text-center">
                    <div class="card-body">
                        <h3>{{ total_vendas }}</h3>
                        <p class="mb-0">Total de Vendas</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success">R$ {{ "%.2f"|format(total_faturamento) }}</h3>
                        <p class="text-muted mb-0">Faturamento Total</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning">R$ {{ "%.2f"|format(total_desconto) }}</h3>
                        <p class="text-muted mb-0">Total em Descontos</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info">R$ {{ "%.2f"|format(total_faturamento / total_vendas if total_vendas > 0 else 0) }}</h3>
                        <p class="text-muted mb-0">Ticket Médio</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Vendas -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Vendas do Período
                </h5>
            </div>
            <div class="card-body">
                {% if vendas %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Data</th>
                                    <th>Cliente</th>
                                    <th>Vendedor</th>
                                    <th>Subtotal</th>
                                    <th>Desconto</th>
                                    <th>Total Final</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for venda in vendas %}
                                <tr>
                                    <td>
                                        <strong>#{{ venda.id }}</strong>
                                    </td>
                                    <td>{{ venda.data_venda }}</td>
                                    <td>{{ venda.cliente_nome or 'Cliente não informado' }}</td>
                                    <td>{{ venda.vendedor_nome }}</td>
                                    <td>R$ {{ "%.2f"|format(venda.subtotal) }}</td>
                                    <td>
                                        {% if venda.desconto_total > 0 %}
                                            <span class="text-warning">R$ {{ "%.2f"|format(venda.desconto_total) }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong class="text-success">R$ {{ "%.2f"|format(venda.total_final) }}</strong>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('venda_sucesso', venda_id=venda.id) }}" class="btn btn-outline-primary" title="Ver Detalhes">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('venda_pdf', venda_id=venda.id) }}" target="_blank" class="btn btn-outline-secondary" title="PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhuma venda encontrada</h5>
                        <p class="text-muted">Tente ajustar os filtros ou período selecionado.</p>
                        <a href="{{ url_for('venda_nova') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Nova Venda
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Análise -->
        {% if vendas %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Análise do Período
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">📊 Estatísticas</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-calendar text-info me-2"></i>
                                Período: {{ data_inicio or 'Início' }} até {{ data_fim or 'Hoje' }}
                            </li>
                            <li><i class="fas fa-percentage text-success me-2"></i>
                                Taxa de desconto média: {{ "%.1f"|format((total_desconto / total_faturamento * 100) if total_faturamento > 0 else 0) }}%
                            </li>
                            <li><i class="fas fa-chart-line text-warning me-2"></i>
                                Média de vendas por dia: {{ "%.1f"|format(total_vendas / 30) }}
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">💡 Insights</h6>
                        <ul class="list-unstyled">
                            {% if total_faturamento > 10000 %}
                            <li><i class="fas fa-trophy text-success me-2"></i>Excelente desempenho de vendas!</li>
                            {% elif total_faturamento > 5000 %}
                            <li><i class="fas fa-thumbs-up text-info me-2"></i>Bom desempenho de vendas</li>
                            {% else %}
                            <li><i class="fas fa-chart-line text-warning me-2"></i>Oportunidade de crescimento</li>
                            {% endif %}
                            
                            {% if (total_desconto / total_faturamento * 100) > 15 %}
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>Alto percentual de descontos</li>
                            {% endif %}
                            
                            <li><i class="fas fa-lightbulb text-primary me-2"></i>Analise produtos mais vendidos para estratégias</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
