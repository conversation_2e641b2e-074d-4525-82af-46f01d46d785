"""
Módulo de autenticação e autorização do sistema Saúde Flex
Gerencia login, logout, sessões e permissões de usuários
"""

from flask import session, request, redirect, url_for, flash, g
from functools import wraps
from datetime import datetime, timedelta
import logging
from models import Usuario
from database import db

# Configurar logging
logger = logging.getLogger(__name__)


class AuthManager:
    """
    Gerenciador de autenticação e autorização
    """
    
    # Definir permissões por tipo de usuário
    PERMISSOES = {
        'admin': [
            'gerenciar_usuarios', 'gerenciar_produtos', 'gerenciar_categorias',
            'gerenciar_clientes', 'gerenciar_agendamentos', 'realizar_vendas',
            'cancelar_vendas', 'visualizar_relatorios', 'fazer_backup',
            'gerenciar_sistema'
        ],
        'gerente': [
            'gerenciar_produtos', 'gerenciar_categorias', 'gerenciar_clientes',
            'gerenciar_agendamentos', 'realizar_vendas', 'cancelar_vendas',
            'visualizar_relatorios'
        ],
        'vendedor': [
            'gerenciar_clientes', 'criar_agendamentos', 'realizar_vendas',
            'visualizar_relatorios_basicos'
        ]
    }
    
    # Rotas que não precisam de autenticação
    ROTAS_PUBLICAS = ['login', 'static', 'auth.login']
    
    # Tempo limite de sessão (em minutos)
    TEMPO_LIMITE_SESSAO = 480  # 8 horas
    
    @staticmethod
    def fazer_login(email, senha, lembrar=False):
        """
        Realiza o login do usuário
        
        Args:
            email (str): Email do usuário
            senha (str): Senha do usuário
            lembrar (bool): Se deve lembrar o login
            
        Returns:
            tuple: (sucesso, mensagem, usuario)
        """
        try:
            # Buscar usuário
            usuario = Usuario.buscar_por_email(email)
            
            if not usuario:
                logger.warning(f"Tentativa de login com email inexistente: {email}")
                return False, "Email ou senha incorretos", None
            
            # Verificar se usuário está ativo
            if not usuario.ativo:
                logger.warning(f"Tentativa de login com usuário inativo: {email}")
                return False, "Usuário inativo. Contate o administrador.", None
            
            # Verificar senha
            if not Usuario.verificar_senha(senha, usuario.senha):
                logger.warning(f"Tentativa de login com senha incorreta: {email}")
                AuthManager._incrementar_tentativas_login(usuario.id)
                return False, "Email ou senha incorretos", None
            
            # Verificar tentativas de login
            if AuthManager._verificar_bloqueio_usuario(usuario.id):
                return False, "Usuário temporariamente bloqueado devido a muitas tentativas de login.", None
            
            # Login bem-sucedido
            AuthManager._criar_sessao(usuario, lembrar)
            AuthManager._registrar_login_sucesso(usuario.id)
            
            logger.info(f"Login bem-sucedido: {email}")
            return True, "Login realizado com sucesso!", usuario
            
        except Exception as e:
            logger.error(f"Erro no login: {e}")
            return False, "Erro interno do sistema", None
    
    @staticmethod
    def _criar_sessao(usuario, lembrar=False):
        """
        Cria a sessão do usuário
        
        Args:
            usuario (Usuario): Objeto do usuário
            lembrar (bool): Se deve lembrar o login
        """
        session.permanent = lembrar
        session['user_id'] = usuario.id
        session['user_nome'] = usuario.nome
        session['user_email'] = usuario.email
        session['user_tipo'] = usuario.tipo
        session['login_time'] = datetime.now().isoformat()
        session['last_activity'] = datetime.now().isoformat()
        
        # Log da ação
        db.log_action(
            usuario_id=usuario.id,
            acao='LOGIN',
            tabela='usuarios',
            registro_id=usuario.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
    
    @staticmethod
    def fazer_logout():
        """
        Realiza o logout do usuário
        
        Returns:
            str: Mensagem de sucesso
        """
        user_id = session.get('user_id')
        
        if user_id:
            # Log da ação
            db.log_action(
                usuario_id=user_id,
                acao='LOGOUT',
                tabela='usuarios',
                registro_id=user_id,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )
            
            logger.info(f"Logout realizado: user_id={user_id}")
        
        session.clear()
        return "Logout realizado com sucesso!"
    
    @staticmethod
    def usuario_logado():
        """
        Retorna o usuário logado
        
        Returns:
            Usuario: Objeto do usuário logado ou None
        """
        user_id = session.get('user_id')
        if user_id:
            return Usuario.buscar_por_id(user_id)
        return None
    
    @staticmethod
    def verificar_permissao(permissao):
        """
        Verifica se o usuário logado tem uma permissão específica
        
        Args:
            permissao (str): Nome da permissão
            
        Returns:
            bool: True se tem permissão
        """
        user_tipo = session.get('user_tipo')
        if not user_tipo:
            return False
        
        permissoes_usuario = AuthManager.PERMISSOES.get(user_tipo, [])
        return permissao in permissoes_usuario
    
    @staticmethod
    def verificar_sessao_valida():
        """
        Verifica se a sessão do usuário ainda é válida
        
        Returns:
            bool: True se sessão é válida
        """
        if 'user_id' not in session:
            return False
        
        # Verificar tempo limite da sessão
        last_activity = session.get('last_activity')
        if last_activity:
            try:
                last_activity_dt = datetime.fromisoformat(last_activity)
                tempo_limite = timedelta(minutes=AuthManager.TEMPO_LIMITE_SESSAO)
                
                if datetime.now() - last_activity_dt > tempo_limite:
                    logger.info(f"Sessão expirada para user_id={session.get('user_id')}")
                    session.clear()
                    return False
                
                # Atualizar última atividade
                session['last_activity'] = datetime.now().isoformat()
                
            except ValueError:
                # Formato de data inválido, limpar sessão
                session.clear()
                return False
        
        return True
    
    @staticmethod
    def _incrementar_tentativas_login(user_id):
        """
        Incrementa o contador de tentativas de login falhadas
        
        Args:
            user_id (int): ID do usuário
        """
        try:
            conn = db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE usuarios 
                SET tentativas_login = tentativas_login + 1 
                WHERE id = ?
            ''', (user_id,))
            
            conn.commit()
            
        except Exception as e:
            logger.error(f"Erro ao incrementar tentativas de login: {e}")
        finally:
            conn.close()
    
    @staticmethod
    def _verificar_bloqueio_usuario(user_id):
        """
        Verifica se o usuário está bloqueado por muitas tentativas
        
        Args:
            user_id (int): ID do usuário
            
        Returns:
            bool: True se usuário está bloqueado
        """
        try:
            conn = db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT tentativas_login FROM usuarios WHERE id = ?', (user_id,))
            row = cursor.fetchone()
            
            if row and row['tentativas_login'] >= 5:
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"Erro ao verificar bloqueio: {e}")
            return False
        finally:
            conn.close()
    
    @staticmethod
    def _registrar_login_sucesso(user_id):
        """
        Registra login bem-sucedido e reseta tentativas
        
        Args:
            user_id (int): ID do usuário
        """
        try:
            conn = db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE usuarios 
                SET tentativas_login = 0, data_ultimo_login = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (user_id,))
            
            conn.commit()
            
        except Exception as e:
            logger.error(f"Erro ao registrar login sucesso: {e}")
        finally:
            conn.close()


# ============================================================================
# DECORADORES DE AUTENTICAÇÃO E AUTORIZAÇÃO
# ============================================================================

def login_required(f):
    """
    Decorador que exige que o usuário esteja logado
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not AuthManager.verificar_sessao_valida():
            flash('Você precisa estar logado para acessar esta página.', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function


def permission_required(permissao):
    """
    Decorador que exige uma permissão específica
    
    Args:
        permissao (str): Nome da permissão necessária
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not AuthManager.verificar_sessao_valida():
                flash('Você precisa estar logado para acessar esta página.', 'warning')
                return redirect(url_for('login'))
            
            if not AuthManager.verificar_permissao(permissao):
                flash('Você não tem permissão para acessar esta página.', 'error')
                return redirect(url_for('index'))
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def admin_required(f):
    """
    Decorador que exige que o usuário seja administrador
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not AuthManager.verificar_sessao_valida():
            flash('Você precisa estar logado para acessar esta página.', 'warning')
            return redirect(url_for('login'))
        
        if session.get('user_tipo') != 'admin':
            flash('Acesso negado! Apenas administradores podem acessar esta área.', 'error')
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return decorated_function


def manager_or_admin_required(f):
    """
    Decorador que exige que o usuário seja gerente ou administrador
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not AuthManager.verificar_sessao_valida():
            flash('Você precisa estar logado para acessar esta página.', 'warning')
            return redirect(url_for('login'))
        
        user_tipo = session.get('user_tipo')
        if user_tipo not in ['admin', 'gerente']:
            flash('Acesso negado! Apenas gerentes e administradores podem acessar esta área.', 'error')
            return redirect(url_for('index'))
        
        return f(*args, **kwargs)
    return decorated_function
