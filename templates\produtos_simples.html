<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .badge-estoque-baixo {
            background-color: #dc3545;
        }
        .badge-estoque-ok {
            background-color: #28a745;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Produtos</h1>
                <p class="text-muted">Gerenciamento de produtos</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('produto_novo') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Novo Produto
                </a>
            </div>
        </div>

        <!-- Lista de Produtos -->
        <div class="card">
            <div class="card-body">
                {% if produtos %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Descrição</th>
                                    <th>Categoria</th>
                                    <th>Preço Custo</th>
                                    <th>Preço Venda</th>
                                    <th>Estoque</th>
                                    <th>Status</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for produto in produtos %}
                                <tr>
                                    <td>#{{ produto.id }}</td>
                                    <td>
                                        <strong>{{ produto.nome }}</strong>
                                    </td>
                                    <td>{{ produto.descricao or '-' }}</td>
                                    <td>{{ produto.categoria_nome or '-' }}</td>
                                    <td>R$ {{ "%.2f"|format(produto.preco_custo) }}</td>
                                    <td>R$ {{ "%.2f"|format(produto.preco_venda) }}</td>
                                    <td>
                                        <span class="badge {{ 'badge-estoque-baixo' if produto.estoque_atual <= produto.estoque_minimo else 'badge-estoque-ok' }}">
                                            {{ produto.estoque_atual }}
                                        </span>
                                        <small class="text-muted">(mín: {{ produto.estoque_minimo }})</small>
                                    </td>
                                    <td>
                                        {% if produto.ativo %}
                                            <span class="badge bg-success">Ativo</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inativo</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="Excluir">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum produto cadastrado</h5>
                        <p class="text-muted">Clique em "Novo Produto" para começar</p>
                        <a href="{{ url_for('produto_novo') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Cadastrar Primeiro Produto
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
