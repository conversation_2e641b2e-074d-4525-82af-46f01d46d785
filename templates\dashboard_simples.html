<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            cursor: pointer;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        .stat-icon.primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .stat-icon.success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
        .stat-icon.warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stat-icon.info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Título -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Dashboard</h1>
                <p class="text-muted">Visão geral do sistema</p>
            </div>
        </div>

        <!-- Cards de Estatísticas -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card h-100" onclick="window.location.href='{{ url_for('produtos') }}'">
                    <div class="card-body d-flex align-items-center">
                        <div class="stat-icon primary me-3">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="stat-number">{{ estatisticas.total_produtos }}</div>
                            <div class="stat-label">Total de Produtos</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card h-100" onclick="window.location.href='{{ url_for('clientes') }}'">
                    <div class="card-body d-flex align-items-center">
                        <div class="stat-icon success me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="stat-number">{{ estatisticas.total_clientes }}</div>
                            <div class="stat-label">Total de Clientes</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card h-100" onclick="window.location.href='{{ url_for('vendas') }}'">
                    <div class="card-body d-flex align-items-center">
                        <div class="stat-icon info me-3">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="stat-number">{{ estatisticas.agendamentos_hoje }}</div>
                            <div class="stat-label">Vendas Hoje</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card stat-card h-100" onclick="window.location.href='{{ url_for('relatorio_estoque_baixo') if session.user_tipo in ['admin', 'gerente'] else '#' }}'">
                    <div class="card-body d-flex align-items-center">
                        <div class="stat-icon warning me-3">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="stat-number">{{ estatisticas.estoque_baixo }}</div>
                            <div class="stat-label">Estoque Baixo</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vendas Recentes -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock me-2"></i>Vendas Recentes
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if vendas_recentes %}
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Cliente</th>
                                            <th>Vendedor</th>
                                            <th>Total</th>
                                            <th>Data</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for venda in vendas_recentes %}
                                        <tr>
                                            <td>#{{ venda.id }}</td>
                                            <td>{{ venda.cliente_nome or 'N/A' }}</td>
                                            <td>{{ venda.vendedor_nome or 'N/A' }}</td>
                                            <td>R$ {{ "%.2f"|format(venda.total_final) }}</td>
                                            <td>{{ venda.data_venda }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Nenhuma venda registrada ainda</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
