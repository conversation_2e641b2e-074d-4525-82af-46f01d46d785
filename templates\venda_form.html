<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Venda - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .produto-item {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
        }
        .total-display {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        .total-value {
            font-size: 2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Nova Venda</h1>
                <p class="text-muted">Registrar nova venda no sistema</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('vendas') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar
                </a>
            </div>
        </div>

        <!-- Formulário -->
        <form method="POST" id="vendaForm">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>Informações da Venda
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="cliente_id" class="form-label">Cliente</label>
                                <select class="form-select" id="cliente_id" name="cliente_id">
                                    <option value="">Selecione um cliente (opcional)</option>
                                    {% for cliente in clientes %}
                                    <option value="{{ cliente.id }}">{{ cliente.nome }} - {{ cliente.telefone }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="desconto_total_valor" class="form-label">Desconto Total (R$)</label>
                                    <div class="input-group">
                                        <span class="input-group-text">R$</span>
                                        <input type="number" class="form-control" id="desconto_total_valor" name="desconto_total_valor"
                                               step="0.01" min="0" value="0" onchange="calcularDescontoTotal('valor')">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="desconto_total_percentual" class="form-label">Desconto Total (%)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="desconto_total_percentual" name="desconto_total_percentual"
                                               step="0.01" min="0" max="100" value="0" onchange="calcularDescontoTotal('percentual')">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="observacoes" class="form-label">Observações</label>
                                <textarea class="form-control" id="observacoes" name="observacoes" rows="2"
                                          placeholder="Observações sobre a venda..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Produtos -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-box me-2"></i>Produtos
                            </h5>
                            <button type="button" class="btn btn-sm btn-primary" onclick="adicionarProduto()">
                                <i class="fas fa-plus me-1"></i>Adicionar Produto
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="produtos-container">
                                <!-- Produtos serão adicionados aqui dinamicamente -->
                            </div>
                            
                            <div class="text-center py-3" id="sem-produtos">
                                <i class="fas fa-box-open fa-2x text-muted mb-2"></i>
                                <p class="text-muted">Nenhum produto adicionado</p>
                                <button type="button" class="btn btn-primary" onclick="adicionarProduto()">
                                    <i class="fas fa-plus me-1"></i>Adicionar Primeiro Produto
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Resumo da Venda -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-calculator me-2"></i>Resumo da Venda
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="total-display">
                                <div class="total-value" id="total-display">R$ 0,00</div>
                                <small>Total da Venda</small>
                            </div>
                            
                            <div class="mt-3">
                                <div class="d-flex justify-content-between">
                                    <span>Subtotal:</span>
                                    <span id="subtotal-display">R$ 0,00</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Desconto:</span>
                                    <span id="desconto-display">R$ 0,00</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold">
                                    <span>Total:</span>
                                    <span id="total-final-display">R$ 0,00</span>
                                </div>
                            </div>

                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-check me-1"></i>Finalizar Venda
                                </button>
                                <a href="{{ url_for('vendas') }}" class="btn btn-secondary">
                                    Cancelar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let produtoIndex = 0;
        const produtos = {{ produtos|tojson }};

        function adicionarProduto() {
            const container = document.getElementById('produtos-container');
            const semProdutos = document.getElementById('sem-produtos');
            
            const produtoHtml = `
                <div class="produto-item" id="produto-${produtoIndex}">
                    <div class="row align-items-end">
                        <div class="col-md-5">
                            <label class="form-label">Produto *</label>
                            <select class="form-select" name="produto_id[]" onchange="atualizarPreco(${produtoIndex})" required>
                                <option value="">Selecione um produto</option>
                                ${produtos.map(p => `<option value="${p.id}" data-preco="${p.preco_venda}" data-estoque="${p.estoque_atual}">${p.nome} - R$ ${p.preco_venda.toFixed(2)} (Est: ${p.estoque_atual})</option>`).join('')}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Qtd *</label>
                            <input type="number" class="form-control" name="quantidade[]" min="1" value="1" onchange="calcularTotal()" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Desc. R$</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="number" class="form-control desconto-valor" step="0.01" min="0" value="0"
                                       onchange="calcularDescontoProduto(${produtoIndex}, 'valor')">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Desc. %</label>
                            <div class="input-group">
                                <input type="number" class="form-control desconto-percentual" step="0.01" min="0" max="100" value="0"
                                       onchange="calcularDescontoProduto(${produtoIndex}, 'percentual')">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <input type="hidden" name="desconto[]" class="desconto-final" value="0">
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-outline-danger d-block" onclick="removerProduto(${produtoIndex})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', produtoHtml);
            semProdutos.style.display = 'none';
            produtoIndex++;
        }

        function removerProduto(index) {
            const produto = document.getElementById(`produto-${index}`);
            produto.remove();
            
            const container = document.getElementById('produtos-container');
            const semProdutos = document.getElementById('sem-produtos');
            
            if (container.children.length === 0) {
                semProdutos.style.display = 'block';
            }
            
            calcularTotal();
        }

        function atualizarPreco(index) {
            calcularTotal();
        }

        function calcularDescontoProduto(index, tipo) {
            const produto = document.getElementById(`produto-${index}`);
            const descontoValor = produto.querySelector('.desconto-valor');
            const descontoPercentual = produto.querySelector('.desconto-percentual');
            const descontoFinal = produto.querySelector('.desconto-final');
            const select = produto.querySelector('select[name="produto_id[]"]');
            const quantidade = parseFloat(produto.querySelector('input[name="quantidade[]"]').value) || 0;

            if (select.value) {
                const option = select.selectedOptions[0];
                const preco = parseFloat(option.dataset.preco) || 0;
                const subtotalItem = preco * quantidade;

                if (tipo === 'valor') {
                    const valorDesconto = parseFloat(descontoValor.value) || 0;
                    const percentualEquivalente = subtotalItem > 0 ? (valorDesconto / subtotalItem) * 100 : 0;
                    descontoPercentual.value = percentualEquivalente.toFixed(2);
                    descontoFinal.value = valorDesconto;
                } else if (tipo === 'percentual') {
                    const percentualDesconto = parseFloat(descontoPercentual.value) || 0;
                    const valorEquivalente = (subtotalItem * percentualDesconto) / 100;
                    descontoValor.value = valorEquivalente.toFixed(2);
                    descontoFinal.value = valorEquivalente;
                }
            }

            calcularTotal();
        }

        function calcularDescontoTotal(tipo) {
            const descontoValor = document.getElementById('desconto_total_valor');
            const descontoPercentual = document.getElementById('desconto_total_percentual');

            // Calcular subtotal atual
            let subtotal = 0;
            const produtosItems = document.querySelectorAll('.produto-item');

            produtosItems.forEach(item => {
                const select = item.querySelector('select[name="produto_id[]"]');
                const quantidade = parseFloat(item.querySelector('input[name="quantidade[]"]').value) || 0;

                if (select.value) {
                    const option = select.selectedOptions[0];
                    const preco = parseFloat(option.dataset.preco) || 0;
                    subtotal += preco * quantidade;
                }
            });

            if (tipo === 'valor') {
                const valorDesconto = parseFloat(descontoValor.value) || 0;
                const percentualEquivalente = subtotal > 0 ? (valorDesconto / subtotal) * 100 : 0;
                descontoPercentual.value = percentualEquivalente.toFixed(2);
            } else if (tipo === 'percentual') {
                const percentualDesconto = parseFloat(descontoPercentual.value) || 0;
                const valorEquivalente = (subtotal * percentualDesconto) / 100;
                descontoValor.value = valorEquivalente.toFixed(2);
            }

            calcularTotal();
        }

        function calcularTotal() {
            let subtotal = 0;
            let descontoItens = 0;

            const produtosItems = document.querySelectorAll('.produto-item');

            produtosItems.forEach(item => {
                const select = item.querySelector('select[name="produto_id[]"]');
                const quantidade = parseFloat(item.querySelector('input[name="quantidade[]"]').value) || 0;
                const descontoFinal = parseFloat(item.querySelector('.desconto-final').value) || 0;

                if (select.value) {
                    const option = select.selectedOptions[0];
                    const preco = parseFloat(option.dataset.preco) || 0;

                    const subtotalItem = preco * quantidade;
                    subtotal += subtotalItem;
                    descontoItens += descontoFinal;
                }
            });

            // Desconto total adicional
            const descontoTotalValor = parseFloat(document.getElementById('desconto_total_valor').value) || 0;
            const descontoTotal = descontoItens + descontoTotalValor;
            const totalFinal = subtotal - descontoTotal;

            document.getElementById('subtotal-display').textContent = `R$ ${subtotal.toFixed(2).replace('.', ',')}`;
            document.getElementById('desconto-display').textContent = `R$ ${descontoTotal.toFixed(2).replace('.', ',')}`;
            document.getElementById('total-final-display').textContent = `R$ ${totalFinal.toFixed(2).replace('.', ',')}`;
            document.getElementById('total-display').textContent = `R$ ${totalFinal.toFixed(2).replace('.', ',')}`;
        }

        // Adicionar primeiro produto automaticamente
        adicionarProduto();
    </script>
</body>
</html>
