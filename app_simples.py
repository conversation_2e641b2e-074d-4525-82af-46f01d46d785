#!/usr/bin/env python3
"""
Saúde Flex - Sistema Simplificado e Funcional
Versão corrigida sem erros de template
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory
import sqlite3
import hashlib
import webbrowser
import threading
import time
import os
from werkzeug.utils import secure_filename
import uuid

# Configurações
DATABASE = 'saude_flex.db'
SECRET_KEY = 'saude_flex_secret_key_2024'
PORT = 5000
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max

# Criar app Flask
app = Flask(__name__)
app.secret_key = SECRET_KEY
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Criar pasta de uploads se não existir
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def hash_password(password):
    """Hash simples da senha usando SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """Verificar senha"""
    return hashlib.sha256(password.encode()).hexdigest() == hashed

def allowed_file(filename):
    """Verificar se arquivo é permitido"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_uploaded_file(file, cliente_id):
    """Salvar arquivo enviado"""
    if file and allowed_file(file.filename):
        # Gerar nome único para o arquivo
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

        # Salvar arquivo
        file.save(file_path)

        # Salvar informações no banco
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO cliente_anexos (cliente_id, nome_arquivo, nome_original, tipo_arquivo, tamanho)
            VALUES (?, ?, ?, ?, ?)
        ''', (cliente_id, unique_filename, file.filename, file_extension, os.path.getsize(file_path)))
        conn.commit()
        conn.close()

        return True
    return False

def get_db():
    """Conectar ao banco de dados"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """Inicializar banco de dados"""
    conn = get_db()
    cursor = conn.cursor()
    
    # Tabela de usuários
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS usuarios (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            senha TEXT NOT NULL,
            tipo TEXT NOT NULL DEFAULT 'vendedor',
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Tabela de categorias
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categorias (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL UNIQUE,
            descricao TEXT,
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Tabela de produtos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS produtos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            descricao TEXT,
            preco_custo REAL NOT NULL DEFAULT 0,
            lucro_desejado REAL NOT NULL DEFAULT 0,
            tipo_lucro TEXT NOT NULL DEFAULT 'percentual' CHECK (tipo_lucro IN ('percentual', 'valor')),
            preco_venda REAL NOT NULL DEFAULT 0,
            categoria_id INTEGER,
            estoque_atual INTEGER DEFAULT 0,
            estoque_minimo INTEGER DEFAULT 5,
            ativo INTEGER DEFAULT 1,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (categoria_id) REFERENCES categorias (id)
        )
    ''')
    
    # Tabela de clientes
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clientes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nome TEXT NOT NULL,
            telefone TEXT NOT NULL,
            email TEXT,
            logradouro TEXT,
            numero TEXT,
            bairro TEXT,
            cidade TEXT,
            uf TEXT,
            cep TEXT,
            observacoes TEXT,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Tabela de anexos de clientes
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cliente_anexos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            nome_arquivo TEXT NOT NULL,
            nome_original TEXT NOT NULL,
            tipo_arquivo TEXT NOT NULL,
            tamanho INTEGER NOT NULL,
            data_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id)
        )
    ''')

    # Tabela de agendamentos
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS agendamentos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER NOT NULL,
            produto_id INTEGER NOT NULL,
            vendedor_id INTEGER NOT NULL,
            data_agendamento DATETIME NOT NULL,
            observacoes TEXT,
            status TEXT NOT NULL DEFAULT 'agendado' CHECK (status IN ('agendado', 'confirmado', 'realizado', 'cancelado')),
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )
    ''')
    
    # Tabela de vendas
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS vendas (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cliente_id INTEGER,
            vendedor_id INTEGER,
            subtotal REAL NOT NULL,
            desconto_total REAL DEFAULT 0,
            total_final REAL NOT NULL,
            observacoes TEXT,
            data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (cliente_id) REFERENCES clientes (id),
            FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
        )
    ''')

    # Tabela de itens de venda
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS itens_venda (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            venda_id INTEGER NOT NULL,
            produto_id INTEGER NOT NULL,
            quantidade INTEGER NOT NULL,
            preco_unitario REAL NOT NULL,
            desconto_item REAL DEFAULT 0,
            subtotal_item REAL NOT NULL,
            FOREIGN KEY (venda_id) REFERENCES vendas (id),
            FOREIGN KEY (produto_id) REFERENCES produtos (id)
        )
    ''')
    
    # Criar usuário admin se não existir
    cursor.execute("SELECT id FROM usuarios WHERE email = '<EMAIL>'")
    if not cursor.fetchone():
        admin_senha = hash_password('admin123')
        cursor.execute('''
            INSERT INTO usuarios (nome, email, senha, tipo)
            VALUES (?, ?, ?, ?)
        ''', ('Administrador', '<EMAIL>', admin_senha, 'admin'))
    
    # Inserir categorias de exemplo
    cursor.execute("SELECT COUNT(*) as total FROM categorias")
    if cursor.fetchone()['total'] == 0:
        categorias_exemplo = [
            ('Suplementos', 'Vitaminas e suplementos alimentares'),
            ('Proteínas', 'Whey protein e proteínas em geral'),
            ('Bem-estar', 'Produtos para bem-estar e saúde'),
            ('Cosméticos', 'Produtos de beleza e cuidados pessoais')
        ]
        cursor.executemany('''
            INSERT INTO categorias (nome, descricao)
            VALUES (?, ?)
        ''', categorias_exemplo)

    # Inserir dados de exemplo se não existirem
    cursor.execute("SELECT COUNT(*) as total FROM produtos")
    if cursor.fetchone()['total'] == 0:
        produtos_exemplo = [
            ('Vitamina C 1000mg', 'Suplemento vitamínico', 15.00, 20.00, 'percentual', 25.00, 1, 50, 10),
            ('Whey Protein', 'Proteína em pó', 80.00, 40.00, 'valor', 120.00, 2, 30, 5),
            ('Ômega 3', 'Cápsulas de ômega 3', 25.00, 15.00, 'valor', 40.00, 1, 40, 8),
            ('Colágeno Hidrolisado', 'Colágeno em pó', 35.00, 30.00, 'percentual', 45.50, 3, 25, 5)
        ]
        cursor.executemany('''
            INSERT INTO produtos (nome, descricao, preco_custo, lucro_desejado, tipo_lucro, preco_venda, categoria_id, estoque_atual, estoque_minimo)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', produtos_exemplo)
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes")
    if cursor.fetchone()['total'] == 0:
        clientes_exemplo = [
            ('João Silva', '(11) 99999-9999', '<EMAIL>', 'Rua das Flores', '123', 'Centro', 'São Paulo', 'SP', '01234-567'),
            ('Maria Santos', '(11) 88888-8888', '<EMAIL>', 'Av. Paulista', '456', 'Bela Vista', 'São Paulo', 'SP', '01310-100'),
            ('Pedro Costa', '(11) 77777-7777', '<EMAIL>', 'Rua Augusta', '789', 'Consolação', 'São Paulo', 'SP', '01305-000'),
            ('Ana Oliveira', '(11) 66666-6666', '<EMAIL>', 'Rua Oscar Freire', '321', 'Jardins', 'São Paulo', 'SP', '01426-001')
        ]
        cursor.executemany('''
            INSERT INTO clientes (nome, telefone, email, logradouro, numero, bairro, cidade, uf, cep)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', clientes_exemplo)
    
    conn.commit()
    conn.close()

def login_required(f):
    """Decorador para verificar login"""
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# Rotas principais
@app.route('/')
@login_required
def index():
    """Dashboard principal"""
    conn = get_db()
    cursor = conn.cursor()
    
    # Estatísticas básicas
    cursor.execute("SELECT COUNT(*) as total FROM produtos WHERE ativo = 1")
    total_produtos = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM clientes")
    total_clientes = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM vendas WHERE DATE(data_venda) = DATE('now')")
    vendas_hoje = cursor.fetchone()['total']
    
    cursor.execute("SELECT COUNT(*) as total FROM produtos WHERE estoque_atual <= estoque_minimo AND ativo = 1")
    estoque_baixo = cursor.fetchone()['total']
    
    # Vendas recentes
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
        FROM vendas v
        LEFT JOIN clientes c ON v.cliente_id = c.id
        LEFT JOIN usuarios u ON v.vendedor_id = u.id
        ORDER BY v.data_venda DESC
        LIMIT 5
    ''')
    vendas_recentes = [dict(row) for row in cursor.fetchall()]
    
    conn.close()
    
    # Dados organizados para o template
    estatisticas = {
        'total_produtos': total_produtos,
        'total_clientes': total_clientes,
        'agendamentos_hoje': vendas_hoje,
        'estoque_baixo': estoque_baixo
    }
    
    return render_template('dashboard_simples.html',
                         estatisticas=estatisticas,
                         vendas_recentes=vendas_recentes)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Página de login"""
    if request.method == 'POST':
        email = request.form['email']
        senha = request.form['senha']
        
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM usuarios WHERE email = ? AND ativo = 1", (email,))
        user = cursor.fetchone()
        conn.close()
        
        if user and verify_password(senha, user['senha']):
            session['user_id'] = user['id']
            session['user_nome'] = user['nome']
            session['user_tipo'] = user['tipo']
            flash('Login realizado com sucesso!', 'success')
            return redirect(url_for('index'))
        else:
            flash('Email ou senha incorretos!', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    flash('Logout realizado com sucesso!', 'success')
    return redirect(url_for('login'))

@app.route('/produtos')
@login_required
def produtos():
    """Lista de produtos"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT p.*, c.nome as categoria_nome
        FROM produtos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE p.ativo = 1
        ORDER BY p.nome
    ''')
    produtos_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('produtos_simples.html', produtos=produtos_lista)

@app.route('/produtos/novo', methods=['GET', 'POST'])
@login_required
def produto_novo():
    """Cadastro de novo produto"""
    if request.method == 'POST':
        try:
            nome = request.form['nome']
            descricao = request.form.get('descricao', '')
            preco_custo = float(request.form['preco_custo'])
            lucro_desejado = float(request.form['lucro_desejado'])
            tipo_lucro = request.form['tipo_lucro']
            categoria_id = request.form.get('categoria_id')
            estoque_atual = int(request.form['estoque_atual'])
            estoque_minimo = int(request.form['estoque_minimo'])

            # Calcular preço de venda
            if tipo_lucro == 'percentual':
                preco_venda = preco_custo * (1 + lucro_desejado / 100)
            else:  # valor
                preco_venda = preco_custo + lucro_desejado

            conn = get_db()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO produtos (nome, descricao, preco_custo, lucro_desejado, tipo_lucro,
                                    preco_venda, categoria_id, estoque_atual, estoque_minimo)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (nome, descricao, preco_custo, lucro_desejado, tipo_lucro,
                  preco_venda, categoria_id if categoria_id else None, estoque_atual, estoque_minimo))
            conn.commit()
            conn.close()

            flash('Produto cadastrado com sucesso!', 'success')
            return redirect(url_for('produtos'))

        except Exception as e:
            flash(f'Erro ao cadastrar produto: {str(e)}', 'error')

    # Buscar categorias para o formulário
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
    categorias = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('produto_form.html', categorias=categorias)

@app.route('/categorias')
@login_required
def categorias():
    """Lista de categorias"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado! Apenas administradores e gerentes podem gerenciar categorias.', 'error')
        return redirect(url_for('index'))

    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
    categorias_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('categorias_lista.html', categorias=categorias_lista)

@app.route('/categorias/nova', methods=['GET', 'POST'])
@login_required
def categoria_nova():
    """Cadastro de nova categoria"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado! Apenas administradores e gerentes podem criar categorias.', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        try:
            nome = request.form['nome']
            descricao = request.form.get('descricao', '')

            conn = get_db()
            cursor = conn.cursor()

            # Verificar se categoria já existe
            cursor.execute("SELECT id FROM categorias WHERE nome = ?", (nome,))
            if cursor.fetchone():
                flash('Esta categoria já existe!', 'error')
                return render_template('categoria_form.html')

            cursor.execute('''
                INSERT INTO categorias (nome, descricao)
                VALUES (?, ?)
            ''', (nome, descricao or None))
            conn.commit()
            conn.close()

            flash('Categoria cadastrada com sucesso!', 'success')
            return redirect(url_for('categorias'))

        except Exception as e:
            flash(f'Erro ao cadastrar categoria: {str(e)}', 'error')

    return render_template('categoria_form.html')

@app.route('/categorias/editar/<int:categoria_id>', methods=['GET', 'POST'])
@login_required
def categoria_editar(categoria_id):
    """Editar categoria"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    conn = get_db()
    cursor = conn.cursor()

    if request.method == 'POST':
        try:
            nome = request.form['nome']
            descricao = request.form.get('descricao', '')

            cursor.execute('''
                UPDATE categorias
                SET nome = ?, descricao = ?
                WHERE id = ?
            ''', (nome, descricao or None, categoria_id))
            conn.commit()
            conn.close()

            flash('Categoria atualizada com sucesso!', 'success')
            return redirect(url_for('categorias'))

        except Exception as e:
            flash(f'Erro ao atualizar categoria: {str(e)}', 'error')

    # Buscar categoria para edição
    cursor.execute('SELECT * FROM categorias WHERE id = ?', (categoria_id,))
    categoria = cursor.fetchone()
    conn.close()

    if not categoria:
        flash('Categoria não encontrada!', 'error')
        return redirect(url_for('categorias'))

    return render_template('categoria_form.html', categoria=dict(categoria))

@app.route('/categorias/excluir/<int:categoria_id>', methods=['POST'])
@login_required
def categoria_excluir(categoria_id):
    """Excluir categoria"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    try:
        conn = get_db()
        cursor = conn.cursor()

        # Verificar se há produtos usando esta categoria
        cursor.execute('SELECT COUNT(*) as total FROM produtos WHERE categoria_id = ? AND ativo = 1', (categoria_id,))
        produtos_count = cursor.fetchone()['total']

        if produtos_count > 0:
            flash(f'Não é possível excluir esta categoria pois há {produtos_count} produto(s) vinculado(s) a ela.', 'error')
        else:
            cursor.execute('UPDATE categorias SET ativo = 0 WHERE id = ?', (categoria_id,))
            conn.commit()
            flash('Categoria excluída com sucesso!', 'success')

        conn.close()

    except Exception as e:
        flash(f'Erro ao excluir categoria: {str(e)}', 'error')

    return redirect(url_for('categorias'))

@app.route('/clientes')
@login_required
def clientes():
    """Lista de clientes"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM clientes ORDER BY nome')
    clientes_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('clientes_simples.html', clientes=clientes_lista)

@app.route('/clientes/novo', methods=['GET', 'POST'])
@login_required
def cliente_novo():
    """Cadastro de novo cliente"""
    if request.method == 'POST':
        try:
            nome = request.form['nome']
            telefone = request.form['telefone']
            email = request.form.get('email', '')
            logradouro = request.form.get('logradouro', '')
            numero = request.form.get('numero', '')
            bairro = request.form.get('bairro', '')
            cidade = request.form.get('cidade', '')
            uf = request.form.get('uf', '')
            cep = request.form.get('cep', '')
            observacoes = request.form.get('observacoes', '')

            conn = get_db()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO clientes (nome, telefone, email, logradouro, numero, bairro, cidade, uf, cep, observacoes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (nome, telefone, email or None, logradouro or None, numero or None,
                  bairro or None, cidade or None, uf or None, cep or None, observacoes or None))

            cliente_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # Processar arquivos enviados
            if 'anexos' in request.files:
                files = request.files.getlist('anexos')
                for file in files:
                    if file and file.filename:
                        if save_uploaded_file(file, cliente_id):
                            flash(f'Arquivo {file.filename} enviado com sucesso!', 'success')
                        else:
                            flash(f'Erro ao enviar arquivo {file.filename}. Verifique o formato.', 'warning')

            flash('Cliente cadastrado com sucesso!', 'success')
            return redirect(url_for('clientes'))

        except Exception as e:
            flash(f'Erro ao cadastrar cliente: {str(e)}', 'error')

    return render_template('cliente_form.html')

@app.route('/vendas')
@login_required
def vendas():
    """Lista de vendas"""
    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
        FROM vendas v
        LEFT JOIN clientes c ON v.cliente_id = c.id
        LEFT JOIN usuarios u ON v.vendedor_id = u.id
        ORDER BY v.data_venda DESC
    ''')
    vendas_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return render_template('vendas_simples.html', vendas=vendas_lista)

@app.route('/vendas/nova', methods=['GET', 'POST'])
@login_required
def venda_nova():
    """Nova venda"""
    if request.method == 'POST':
        try:
            cliente_id = request.form.get('cliente_id')
            produtos_ids = request.form.getlist('produto_id[]')
            quantidades = request.form.getlist('quantidade[]')
            descontos = request.form.getlist('desconto[]')
            desconto_total_valor = float(request.form.get('desconto_total_valor', 0))
            observacoes = request.form.get('observacoes', '')

            if not produtos_ids:
                flash('Adicione pelo menos um produto à venda!', 'error')
                return redirect(url_for('venda_nova'))

            conn = get_db()
            cursor = conn.cursor()

            # Calcular totais
            subtotal = 0
            desconto_total = 0

            # Validar produtos e calcular valores
            itens_venda = []
            for i, produto_id in enumerate(produtos_ids):
                if not produto_id:
                    continue

                quantidade = int(quantidades[i]) if i < len(quantidades) else 1
                desconto_item = float(descontos[i]) if i < len(descontos) and descontos[i] else 0

                # Buscar produto
                cursor.execute('SELECT * FROM produtos WHERE id = ? AND ativo = 1', (produto_id,))
                produto = cursor.fetchone()

                if not produto:
                    flash(f'Produto ID {produto_id} não encontrado!', 'error')
                    conn.close()
                    return redirect(url_for('venda_nova'))

                # Verificar estoque
                if produto['estoque_atual'] < quantidade:
                    flash(f'Estoque insuficiente para {produto["nome"]}! Disponível: {produto["estoque_atual"]}', 'error')
                    conn.close()
                    return redirect(url_for('venda_nova'))

                preco_unitario = produto['preco_venda']
                subtotal_item = (preco_unitario * quantidade) - desconto_item

                itens_venda.append({
                    'produto_id': produto_id,
                    'quantidade': quantidade,
                    'preco_unitario': preco_unitario,
                    'desconto_item': desconto_item,
                    'subtotal_item': subtotal_item
                })

                subtotal += subtotal_item
                desconto_total += desconto_item

            # Adicionar desconto total da venda
            desconto_total += desconto_total_valor
            total_final = subtotal - desconto_total

            # Inserir venda
            cursor.execute('''
                INSERT INTO vendas (cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (cliente_id or None, session['user_id'], subtotal, desconto_total, total_final, observacoes or None))

            venda_id = cursor.lastrowid

            # Inserir itens da venda e atualizar estoque
            for item in itens_venda:
                cursor.execute('''
                    INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (venda_id, item['produto_id'], item['quantidade'], item['preco_unitario'],
                      item['desconto_item'], item['subtotal_item']))

                # Atualizar estoque
                cursor.execute('''
                    UPDATE produtos
                    SET estoque_atual = estoque_atual - ?
                    WHERE id = ?
                ''', (item['quantidade'], item['produto_id']))

            conn.commit()
            conn.close()

            flash(f'Venda #{venda_id} realizada com sucesso! Total: R$ {total_final:.2f}', 'success')
            return redirect(url_for('venda_sucesso', venda_id=venda_id))

        except Exception as e:
            flash(f'Erro ao realizar venda: {str(e)}', 'error')

    # Buscar dados para o formulário
    conn = get_db()
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM clientes ORDER BY nome')
    clientes_lista = [dict(row) for row in cursor.fetchall()]

    cursor.execute('SELECT * FROM produtos WHERE ativo = 1 AND estoque_atual > 0 ORDER BY nome')
    produtos_lista = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('venda_form.html', clientes=clientes_lista, produtos=produtos_lista)

@app.route('/vendas/sucesso/<int:venda_id>')
@login_required
def venda_sucesso(venda_id):
    """Página de sucesso da venda com opções de impressão e PDF"""
    conn = get_db()
    cursor = conn.cursor()

    # Buscar dados da venda
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
               u.nome as vendedor_nome
        FROM vendas v
        LEFT JOIN clientes c ON v.cliente_id = c.id
        LEFT JOIN usuarios u ON v.vendedor_id = u.id
        WHERE v.id = ?
    ''', (venda_id,))
    venda = cursor.fetchone()

    if not venda:
        flash('Venda não encontrada!', 'error')
        return redirect(url_for('vendas'))

    # Buscar itens da venda
    cursor.execute('''
        SELECT iv.*, p.nome as produto_nome
        FROM itens_venda iv
        JOIN produtos p ON iv.produto_id = p.id
        WHERE iv.venda_id = ?
    ''', (venda_id,))
    itens = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('venda_sucesso.html', venda=dict(venda), itens=itens)

@app.route('/vendas/pdf/<int:venda_id>')
@login_required
def venda_pdf(venda_id):
    """Gerar PDF da venda"""
    conn = get_db()
    cursor = conn.cursor()

    # Buscar dados da venda
    cursor.execute('''
        SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
               c.email as cliente_email, c.logradouro, c.numero, c.bairro, c.cidade, c.uf,
               u.nome as vendedor_nome
        FROM vendas v
        LEFT JOIN clientes c ON v.cliente_id = c.id
        LEFT JOIN usuarios u ON v.vendedor_id = u.id
        WHERE v.id = ?
    ''', (venda_id,))
    venda = cursor.fetchone()

    if not venda:
        flash('Venda não encontrada!', 'error')
        return redirect(url_for('vendas'))

    # Buscar itens da venda
    cursor.execute('''
        SELECT iv.*, p.nome as produto_nome
        FROM itens_venda iv
        JOIN produtos p ON iv.produto_id = p.id
        WHERE iv.venda_id = ?
    ''', (venda_id,))
    itens = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('venda_pdf.html', venda=dict(venda), itens=itens)

@app.route('/relatorios')
@login_required
def relatorios():
    """Dashboard de relatórios"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado! Apenas administradores e gerentes podem acessar relatórios.', 'error')
        return redirect(url_for('index'))

    return render_template('relatorios_dashboard.html')

@app.route('/relatorios/estoque-baixo')
@login_required
def relatorio_estoque_baixo():
    """Relatório de produtos com estoque baixo"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    conn = get_db()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT p.*, c.nome as categoria_nome,
               (SELECT MAX(v.data_venda)
                FROM vendas v
                JOIN itens_venda iv ON v.id = iv.venda_id
                WHERE iv.produto_id = p.id) as ultima_venda
        FROM produtos p
        LEFT JOIN categorias c ON p.categoria_id = c.id
        WHERE p.ativo = 1 AND p.estoque_atual <= p.estoque_minimo
        ORDER BY (p.estoque_atual - p.estoque_minimo) ASC
    ''')
    produtos = [dict(row) for row in cursor.fetchall()]

    # Buscar categorias para filtro
    cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
    categorias = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('relatorio_estoque_baixo.html', produtos=produtos, categorias=categorias)

@app.route('/relatorios/vendas-periodo')
@login_required
def relatorio_vendas_periodo():
    """Relatório de vendas por período"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    data_inicio = request.args.get('data_inicio', '')
    data_fim = request.args.get('data_fim', '')
    vendedor_id = request.args.get('vendedor_id', '')

    conn = get_db()
    cursor = conn.cursor()

    # Query base
    query = '''
        SELECT v.*, c.nome as cliente_nome, u.nome as vendedor_nome
        FROM vendas v
        LEFT JOIN clientes c ON v.cliente_id = c.id
        LEFT JOIN usuarios u ON v.vendedor_id = u.id
        WHERE 1=1
    '''
    params = []

    # Aplicar filtros
    if data_inicio:
        query += ' AND DATE(v.data_venda) >= ?'
        params.append(data_inicio)

    if data_fim:
        query += ' AND DATE(v.data_venda) <= ?'
        params.append(data_fim)

    if vendedor_id:
        query += ' AND v.vendedor_id = ?'
        params.append(vendedor_id)

    query += ' ORDER BY v.data_venda DESC'

    cursor.execute(query, params)
    vendas = [dict(row) for row in cursor.fetchall()]

    # Calcular totais
    total_vendas = len(vendas)
    total_faturamento = sum(v['total_final'] for v in vendas)
    total_desconto = sum(v['desconto_total'] for v in vendas)

    # Buscar vendedores para filtro
    cursor.execute('SELECT * FROM usuarios WHERE tipo IN ("vendedor", "gerente", "admin") ORDER BY nome')
    vendedores = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('relatorio_vendas_periodo.html',
                         vendas=vendas, vendedores=vendedores,
                         total_vendas=total_vendas, total_faturamento=total_faturamento,
                         total_desconto=total_desconto,
                         data_inicio=data_inicio, data_fim=data_fim, vendedor_id=vendedor_id)

@app.route('/relatorios/produtos-mais-vendidos')
@login_required
def relatorio_produtos_mais_vendidos():
    """Relatório de produtos mais vendidos"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    data_inicio = request.args.get('data_inicio', '')
    data_fim = request.args.get('data_fim', '')

    conn = get_db()
    cursor = conn.cursor()

    query = '''
        SELECT p.nome as produto_nome, c.nome as categoria_nome,
               SUM(iv.quantidade) as quantidade_vendida,
               SUM(iv.subtotal_item) as receita_bruta,
               COUNT(DISTINCT v.id) as numero_vendas
        FROM itens_venda iv
        JOIN produtos p ON iv.produto_id = p.id
        LEFT JOIN categorias c ON p.categoria_id = c.id
        JOIN vendas v ON iv.venda_id = v.id
        WHERE 1=1
    '''
    params = []

    if data_inicio:
        query += ' AND DATE(v.data_venda) >= ?'
        params.append(data_inicio)

    if data_fim:
        query += ' AND DATE(v.data_venda) <= ?'
        params.append(data_fim)

    query += '''
        GROUP BY p.id, p.nome, c.nome
        ORDER BY quantidade_vendida DESC
        LIMIT 20
    '''

    cursor.execute(query, params)
    produtos = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('relatorio_produtos_mais_vendidos.html',
                         produtos=produtos, data_inicio=data_inicio, data_fim=data_fim)

@app.route('/relatorios/vendas-por-vendedor')
@login_required
def relatorio_vendas_por_vendedor():
    """Relatório de vendas por vendedor"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado!', 'error')
        return redirect(url_for('index'))

    data_inicio = request.args.get('data_inicio', '')
    data_fim = request.args.get('data_fim', '')

    conn = get_db()
    cursor = conn.cursor()

    query = '''
        SELECT u.nome as vendedor_nome, u.id as vendedor_id,
               COUNT(v.id) as quantidade_vendas,
               SUM(v.total_final) as valor_total,
               AVG(v.total_final) as ticket_medio
        FROM usuarios u
        LEFT JOIN vendas v ON u.id = v.vendedor_id
        WHERE u.tipo IN ('vendedor', 'gerente', 'admin')
    '''
    params = []

    if data_inicio:
        query += ' AND (v.id IS NULL OR DATE(v.data_venda) >= ?)'
        params.append(data_inicio)

    if data_fim:
        query += ' AND (v.id IS NULL OR DATE(v.data_venda) <= ?)'
        params.append(data_fim)

    query += '''
        GROUP BY u.id, u.nome
        ORDER BY valor_total DESC NULLS LAST
    '''

    cursor.execute(query, params)
    vendedores = [dict(row) for row in cursor.fetchall()]

    conn.close()

    return render_template('relatorio_vendas_por_vendedor.html',
                         vendedores=vendedores, data_inicio=data_inicio, data_fim=data_fim)

@app.route('/vendas/cancelar/<int:venda_id>', methods=['POST'])
@login_required
def cancelar_venda(venda_id):
    """Cancelar venda e retornar produtos ao estoque"""
    if session.get('user_tipo') not in ['admin', 'gerente']:
        flash('Acesso negado! Apenas administradores e gerentes podem cancelar vendas.', 'error')
        return redirect(url_for('vendas'))

    try:
        conn = get_db()
        cursor = conn.cursor()

        # Verificar se a venda existe
        cursor.execute('SELECT * FROM vendas WHERE id = ?', (venda_id,))
        venda = cursor.fetchone()

        if not venda:
            flash('Venda não encontrada!', 'error')
            return redirect(url_for('vendas'))

        # Buscar itens da venda para retornar ao estoque
        cursor.execute('SELECT * FROM itens_venda WHERE venda_id = ?', (venda_id,))
        itens = cursor.fetchall()

        # Retornar produtos ao estoque
        for item in itens:
            cursor.execute('''
                UPDATE produtos
                SET estoque_atual = estoque_atual + ?
                WHERE id = ?
            ''', (item['quantidade'], item['produto_id']))

        # Marcar venda como cancelada (adicionar campo status se não existir)
        cursor.execute('''
            UPDATE vendas
            SET observacoes = COALESCE(observacoes, '') || ' [VENDA CANCELADA]'
            WHERE id = ?
        ''', (venda_id,))

        # Inserir registro de cancelamento na tabela de itens (para auditoria)
        cursor.execute('''
            INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
            SELECT venda_id, produto_id, -quantidade, preco_unitario, desconto_item, -subtotal_item
            FROM itens_venda
            WHERE venda_id = ?
        ''', (venda_id,))

        conn.commit()
        conn.close()

        flash(f'Venda #{venda_id} cancelada com sucesso! Produtos retornados ao estoque.', 'success')

    except Exception as e:
        flash(f'Erro ao cancelar venda: {str(e)}', 'error')

    return redirect(url_for('vendas'))

@app.route('/usuarios')
@login_required
def usuarios():
    """Lista de usuários (apenas admin)"""
    if session.get('user_tipo') != 'admin':
        flash('Acesso negado! Apenas administradores podem gerenciar usuários.', 'error')
        return redirect(url_for('index'))

    conn = get_db()
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM usuarios ORDER BY nome')
    usuarios_lista = [dict(row) for row in cursor.fetchall()]
    conn.close()

    return render_template('usuarios_simples.html', usuarios=usuarios_lista)

@app.route('/usuarios/novo', methods=['GET', 'POST'])
@login_required
def usuario_novo():
    """Cadastro de novo usuário (apenas admin)"""
    if session.get('user_tipo') != 'admin':
        flash('Acesso negado! Apenas administradores podem criar usuários.', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        try:
            nome = request.form['nome']
            email = request.form['email']
            senha = request.form['senha']
            tipo = request.form['tipo']

            # Verificar se email já existe
            conn = get_db()
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM usuarios WHERE email = ?", (email,))
            if cursor.fetchone():
                flash('Este email já está cadastrado!', 'error')
                return render_template('usuario_form.html')

            # Hash da senha
            senha_hash = hash_password(senha)

            cursor.execute('''
                INSERT INTO usuarios (nome, email, senha, tipo)
                VALUES (?, ?, ?, ?)
            ''', (nome, email, senha_hash, tipo))
            conn.commit()
            conn.close()

            flash('Usuário cadastrado com sucesso!', 'success')
            return redirect(url_for('usuarios'))

        except Exception as e:
            flash(f'Erro ao cadastrar usuário: {str(e)}', 'error')

    return render_template('usuario_form.html')

@app.route('/api/calcular-preco', methods=['POST'])
@login_required
def calcular_preco():
    """API para calcular preço de venda"""
    try:
        data = request.get_json()
        preco_custo = float(data['preco_custo'])
        lucro_desejado = float(data['lucro_desejado'])
        tipo_lucro = data['tipo_lucro']

        if tipo_lucro == 'percentual':
            preco_venda = preco_custo * (1 + lucro_desejado / 100)
        else:  # valor
            preco_venda = preco_custo + lucro_desejado

        return jsonify({'preco_venda': round(preco_venda, 2)})

    except Exception as e:
        return jsonify({'error': str(e)}), 400

def abrir_navegador():
    """Abrir navegador automaticamente"""
    time.sleep(2)
    webbrowser.open(f'http://localhost:{PORT}')

if __name__ == '__main__':
    print("🚀 Iniciando Saúde Flex...")
    print("📁 Inicializando banco de dados...")
    
    try:
        init_database()
        print("✅ Banco de dados inicializado")
        print("🌐 Iniciando servidor Flask...")
        print(f"📍 Acesse: http://localhost:{PORT}")
        print("🔑 Login: <EMAIL> / admin123")
        print("-" * 50)
        
        # Abrir navegador automaticamente
        threading.Thread(target=abrir_navegador, daemon=True).start()
        
        app.run(debug=False, host='127.0.0.1', port=PORT)
        
    except Exception as e:
        print(f"❌ Erro ao iniciar: {e}")
        import traceback
        traceback.print_exc()
        input("Pressione Enter para sair...")
