/* <PERSON><PERSON><PERSON> Flex - Modern UX/UI Design System */

:root {
    /* Primary Brand Colors */
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --primary-light: #8fa4f3;
    --secondary-color: #764ba2;
    --secondary-dark: #6a4190;
    --secondary-light: #9d7bb8;

    /* Semantic Colors */
    --success-color: #10b981;
    --success-light: #34d399;
    --info-color: #3b82f6;
    --info-light: #60a5fa;
    --warning-color: #f59e0b;
    --warning-light: #fbbf24;
    --danger-color: #ef4444;
    --danger-light: #f87171;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Legacy Support */
    --light-color: var(--gray-50);
    --dark-color: var(--gray-700);

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* Global Styles */
body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    color: var(--gray-700);
    line-height: 1.6;
    font-size: 0.95rem;
    min-height: 100vh;
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--gray-800);
    letter-spacing: -0.025em;
}

.h1, .h2, .h3, .h4, .h5, .h6 {
    font-weight: 600;
    color: var(--gray-800);
    letter-spacing: -0.025em;
}

/* Enhanced Navbar */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md) 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.navbar-brand i {
    font-size: 1.75rem;
    background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9) !important;
    transition: var(--transition-normal);
    padding: var(--spacing-sm) var(--spacing-md) !important;
    border-radius: var(--radius-md);
    margin: 0 var(--spacing-xs);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white !important;
}

.navbar-nav .dropdown-menu {
    background: white;
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.navbar-nav .dropdown-item {
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: var(--transition-fast);
    color: var(--gray-700);
}

.navbar-nav .dropdown-item:hover {
    background: var(--gray-100);
    color: var(--primary-color);
    transform: translateX(4px);
}

/* Modern Card System */
.card {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    background: white;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

.card-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-bottom: 1px solid var(--gray-200);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0 !important;
    padding: var(--spacing-lg);
    position: relative;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: rotate(45deg);
}

.dashboard-card .card-body {
    position: relative;
    z-index: 1;
}

.dashboard-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

/* Stat Cards */
.stat-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1;
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.stat-icon.success {
    background: linear-gradient(135deg, var(--success-color), var(--success-light));
    color: white;
}

.stat-icon.info {
    background: linear-gradient(135deg, var(--info-color), var(--info-light));
    color: white;
}

.stat-icon.warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
    color: white;
}

/* Modern Button System */
.btn {
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.875rem;
    padding: var(--spacing-sm) var(--spacing-lg);
    transition: var(--transition-normal);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-color);
    color: white;
    position: relative;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
    border-color: var(--primary-dark);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-light) 100%);
    border-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);
    border-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-light) 100%);
    border-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    border-color: var(--gray-300);
    color: var(--gray-600);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--gray-600);
    border-color: var(--gray-600);
    color: white;
}

/* Button Sizes */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.8rem;
    border-radius: var(--radius-sm);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1rem;
    border-radius: var(--radius-lg);
}

/* Button Groups */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.btn-group .btn:only-child {
    border-radius: var(--radius-md);
}

/* Modern Form System */
.form-control {
    border-radius: var(--radius-md);
    border: 2px solid var(--gray-200);
    background: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
    transition: var(--transition-normal);
    color: var(--gray-700);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
    background: white;
}

.form-control:hover:not(:focus) {
    border-color: var(--gray-300);
}

.form-control::placeholder {
    color: var(--gray-400);
    font-weight: 400;
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

.form-text {
    color: var(--gray-500);
    font-size: 0.8rem;
    margin-top: var(--spacing-xs);
}

/* Input Groups */
.input-group {
    position: relative;
}

.input-group .form-control {
    border-radius: 0;
}

.input-group .form-control:first-child {
    border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.input-group .form-control:last-child {
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.input-group-text {
    background: var(--gray-100);
    border: 2px solid var(--gray-200);
    color: var(--gray-600);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Select Styling */
.form-select {
    border-radius: var(--radius-md);
    border: 2px solid var(--gray-200);
    background: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.9rem;
    transition: var(--transition-normal);
    color: var(--gray-700);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* Checkbox and Radio */
.form-check-input {
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-check-label {
    color: var(--gray-700);
    font-weight: 500;
    margin-left: var(--spacing-sm);
}

/* Form Validation */
.is-valid {
    border-color: var(--success-color) !important;
}

.is-invalid {
    border-color: var(--danger-color) !important;
}

.valid-feedback {
    color: var(--success-color);
    font-size: 0.8rem;
    margin-top: var(--spacing-xs);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: 0.8rem;
    margin-top: var(--spacing-xs);
}

/* Floating Labels */
.form-floating {
    position: relative;
}

.form-floating > .form-control {
    padding: 1rem var(--spacing-md) var(--spacing-sm);
}

.form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    padding: 1rem var(--spacing-md);
    pointer-events: none;
    border: 2px solid transparent;
    transform-origin: 0 0;
    transition: var(--transition-normal);
    color: var(--gray-500);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--primary-color);
}

/* Table Enhancements */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem 0.75rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
}

.table tbody td {
    padding: 1rem 0.75rem;
    border-top: 1px solid #e3e6f0;
    vertical-align: middle;
}

/* Badge Enhancements */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 0.75rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: rgba(28, 200, 138, 0.1);
    border-left-color: var(--success-color);
    color: #0f5132;
}

.alert-danger {
    background-color: rgba(231, 74, 59, 0.1);
    border-left-color: var(--danger-color);
    color: #842029;
}

.alert-warning {
    background-color: rgba(246, 194, 62, 0.1);
    border-left-color: var(--warning-color);
    color: #664d03;
}

.alert-info {
    background-color: rgba(54, 185, 204, 0.1);
    border-left-color: var(--info-color);
    color: #055160;
}

/* Dashboard Cards */
.border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
}

.text-xs {
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.text-gray-800 {
    color: var(--dark-color) !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Utilities */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem 0 rgba(58, 59, 69, 0.2) !important;
}

.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.5rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Custom Components */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(102, 126, 234, 0.3);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Product Card */
.product-card {
    border: 1px solid #e3e6f0;
    border-radius: 1rem;
    padding: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.product-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.15);
    transform: translateY(-2px);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.5rem;
    background-color: #f8f9fc;
}

/* Status Indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-active {
    background-color: var(--success-color);
}

.status-inactive {
    background-color: var(--danger-color);
}

.status-pending {
    background-color: var(--warning-color);
}

/* Modern Table System */
.table {
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: white;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.table thead th {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border: none;
    font-weight: 600;
    color: var(--gray-700);
    padding: var(--spacing-lg) var(--spacing-md);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid var(--gray-200);
}

.table tbody tr {
    transition: var(--transition-fast);
    border: none;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    transform: scale(1.01);
}

.table tbody td {
    padding: var(--spacing-lg) var(--spacing-md);
    border-top: 1px solid var(--gray-100);
    vertical-align: middle;
    color: var(--gray-700);
}

.table-responsive {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

/* Modern Badge System */
.badge {
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--success-color), var(--success-light)) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, var(--info-color), var(--info-light)) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-light)) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--danger-color), var(--danger-light)) !important;
}

/* Modern Alert System */
.alert {
    border: none;
    border-radius: var(--radius-lg);
    border-left: 4px solid;
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1));
    transform: skewX(-15deg);
}

.alert-success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(52, 211, 153, 0.05) 100%);
    border-left-color: var(--success-color);
    color: #065f46;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.05) 100%);
    border-left-color: var(--danger-color);
    color: #991b1b;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.05) 100%);
    border-left-color: var(--warning-color);
    color: #92400e;
}

.alert-info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(96, 165, 250, 0.05) 100%);
    border-left-color: var(--info-color);
    color: #1e40af;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: var(--radius-md);
    background: var(--gray-200);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Modern Modal System */
.modal-content {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-lg);
}

.modal-title {
    font-weight: 600;
    color: var(--gray-800);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-md) var(--spacing-lg);
}

/* Footer */
footer {
    margin-top: auto;
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%) !important;
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-xl) 0;
    color: var(--gray-600);
}

/* Calendar Enhancements */
.fc {
    font-family: inherit;
}

.fc-theme-standard .fc-scrollgrid {
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.fc-theme-standard th {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-color: var(--gray-200);
    color: var(--gray-700);
    font-weight: 600;
    padding: var(--spacing-md);
}

.fc-theme-standard td {
    border-color: var(--gray-200);
}

.fc-daygrid-day:hover {
    background: rgba(102, 126, 234, 0.05);
}

.fc-event {
    border-radius: var(--radius-sm);
    border: none;
    padding: 2px 4px;
    font-weight: 500;
    font-size: 0.8rem;
}

.fc-event-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.fc-event-success {
    background: linear-gradient(135deg, var(--success-color), var(--success-light));
    color: white;
}

.fc-event-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
    color: white;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: inherit;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    max-width: 400px;
}

.notification-alert {
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: none;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Utility Classes */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: var(--spacing-md);
    }

    .table-responsive {
        border-radius: var(--radius-md);
    }

    .btn-group .btn {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .navbar-nav .nav-link {
        padding: var(--spacing-sm) !important;
        margin: 0;
    }

    .notification-container {
        left: 10px;
        right: 10px;
        max-width: none;
    }
}

@media (max-width: 576px) {
    .card {
        border-radius: var(--radius-md);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.8rem;
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .modal-dialog {
        margin: var(--spacing-md);
    }
}

/* Dark Mode Support (Future) */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
        --gray-400: #9ca3af;
        --gray-500: #d1d5db;
        --gray-600: #e5e7eb;
        --gray-700: #f3f4f6;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
}
