{% extends "base.html" %}

{% block title %}Calendário de Agendamentos - Saúde Flex{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2 gradient-text">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Calendário de Agendamentos
                </h1>
                <p class="text-muted mb-0">Visualize e gerencie todos os agendamentos em uma interface moderna.</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{{ url_for('agendamentos') }}" class="btn btn-outline-primary">
                    <i class="fas fa-list me-2"></i>
                    Visualizar Lista
                </a>
                <a href="{{ url_for('agendamento_novo') }}" class="btn btn-primary">
                    <i class="fas fa-calendar-plus me-2"></i>
                    Novo Agendamento
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label for="filtro-vendedor" class="form-label">Vendedor</label>
                        <select class="form-select" id="filtro-vendedor">
                            <option value="">Todos os vendedores</option>
                            <!-- Será preenchido via JavaScript -->
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="filtro-status" class="form-label">Status</label>
                        <select class="form-select" id="filtro-status">
                            <option value="">Todos os status</option>
                            <option value="agendado">Agendado</option>
                            <option value="confirmado">Confirmado</option>
                            <option value="realizado">Realizado</option>
                            <option value="cancelado">Cancelado</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="filtro-produto" class="form-label">Produto</label>
                        <select class="form-select" id="filtro-produto">
                            <option value="">Todos os produtos</option>
                            <!-- Será preenchido via JavaScript -->
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-primary me-2" onclick="aplicarFiltros()">
                            <i class="fas fa-filter me-2"></i>
                            Filtrar
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="limparFiltros()">
                            <i class="fas fa-eraser me-2"></i>
                            Limpar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Container -->
<div class="row">
    <div class="col-12">
        <div class="card hover-lift">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar me-2 text-primary"></i>
                    Calendário
                </h5>
                <div class="d-flex gap-2">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="mudarVisualizacao('dayGridMonth')">
                            <i class="fas fa-calendar-alt me-1"></i>
                            Mês
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="mudarVisualizacao('timeGridWeek')">
                            <i class="fas fa-calendar-week me-1"></i>
                            Semana
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="mudarVisualizacao('timeGridDay')">
                            <i class="fas fa-calendar-day me-1"></i>
                            Dia
                        </button>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary" onclick="irParaHoje()">
                        <i class="fas fa-home me-1"></i>
                        Hoje
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="calendar"></div>
            </div>
        </div>
    </div>
</div>

<!-- Legend -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    Legenda
                </h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="badge bg-warning me-2" style="width: 20px; height: 20px;"></div>
                            <span>Agendado</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="badge bg-primary me-2" style="width: 20px; height: 20px;"></div>
                            <span>Confirmado</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="badge bg-success me-2" style="width: 20px; height: 20px;"></div>
                            <span>Realizado</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="badge bg-danger me-2" style="width: 20px; height: 20px;"></div>
                            <span>Cancelado</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Detalhes do Agendamento -->
<div class="modal fade" id="modalAgendamento" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Detalhes do Agendamento
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="conteudo-agendamento">
                <!-- Conteúdo será carregado via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                <button type="button" class="btn btn-warning" id="btn-editar-agendamento">
                    <i class="fas fa-edit me-2"></i>
                    Editar
                </button>
                <div class="btn-group">
                    <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-check me-2"></i>
                        Alterar Status
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="alterarStatusAgendamento('confirmado')">
                            <i class="fas fa-check-circle me-2"></i>Confirmar
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="alterarStatusAgendamento('realizado')">
                            <i class="fas fa-check-double me-2"></i>Marcar como Realizado
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="alterarStatusAgendamento('cancelado')">
                            <i class="fas fa-times me-2"></i>Cancelar
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal para Novo Agendamento -->
<div class="modal fade" id="modalNovoAgendamento" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-calendar-plus me-2"></i>
                    Novo Agendamento
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="form-novo-agendamento">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="cliente-select" class="form-label">Cliente</label>
                            <select class="form-select" id="cliente-select" required>
                                <option value="">Selecione um cliente</option>
                                <!-- Será preenchido via JavaScript -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="produto-select" class="form-label">Produto</label>
                            <select class="form-select" id="produto-select" required>
                                <option value="">Selecione um produto</option>
                                <!-- Será preenchido via JavaScript -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="vendedor-select" class="form-label">Vendedor</label>
                            <select class="form-select" id="vendedor-select" required>
                                <option value="">Selecione um vendedor</option>
                                <!-- Será preenchido via JavaScript -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="data-agendamento" class="form-label">Data e Hora</label>
                            <input type="datetime-local" class="form-control" id="data-agendamento" required>
                        </div>
                        <div class="col-12 mb-3">
                            <label for="observacoes-agendamento" class="form-label">Observações</label>
                            <textarea class="form-control" id="observacoes-agendamento" rows="3" placeholder="Observações sobre o agendamento..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarNovoAgendamento()">
                    <i class="fas fa-save me-2"></i>
                    Salvar Agendamento
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
<style>
/* Calendar customizations */
.fc {
    font-family: inherit;
}

.fc-theme-standard .fc-scrollgrid {
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid var(--gray-200);
}

.fc-theme-standard th {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
    border-color: var(--gray-200);
    color: var(--gray-700);
    font-weight: 600;
    padding: var(--spacing-md);
}

.fc-theme-standard td {
    border-color: var(--gray-200);
}

.fc-daygrid-day:hover {
    background: rgba(102, 126, 234, 0.05);
}

.fc-event {
    border-radius: var(--radius-sm);
    border: none;
    padding: 2px 6px;
    font-weight: 500;
    font-size: 0.8rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.fc-event:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.fc-event.fc-event-agendado {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-light));
    color: white;
}

.fc-event.fc-event-confirmado {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
}

.fc-event.fc-event-realizado {
    background: linear-gradient(135deg, var(--success-color), var(--success-light));
    color: white;
}

.fc-event.fc-event-cancelado {
    background: linear-gradient(135deg, var(--danger-color), var(--danger-light));
    color: white;
}

.fc-toolbar {
    margin-bottom: var(--spacing-lg) !important;
}

.fc-toolbar-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: var(--gray-800) !important;
}

.fc-button {
    background: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    border-radius: var(--radius-md) !important;
    font-weight: 500 !important;
    padding: var(--spacing-sm) var(--spacing-md) !important;
}

.fc-button:hover {
    background: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
}

.fc-button:disabled {
    background: var(--gray-300) !important;
    border-color: var(--gray-300) !important;
}

.fc-today-button {
    background: var(--success-color) !important;
    border-color: var(--success-color) !important;
}

.fc-today-button:hover {
    background: var(--success-light) !important;
    border-color: var(--success-light) !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/locales/pt-br.global.min.js"></script>

<script>
let calendar;
let agendamentoAtual = null;

// Initialize calendar
document.addEventListener('DOMContentLoaded', function() {
    const calendarEl = document.getElementById('calendar');

    calendar = new FullCalendar.Calendar(calendarEl, {
        locale: 'pt-br',
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        height: 'auto',
        events: carregarAgendamentos,
        eventClick: function(info) {
            mostrarDetalhesAgendamento(info.event);
        },
        dateClick: function(info) {
            abrirModalNovoAgendamento(info.date);
        },
        eventDidMount: function(info) {
            // Add tooltip
            info.el.setAttribute('title', info.event.title);
            info.el.setAttribute('data-bs-toggle', 'tooltip');
        }
    });

    calendar.render();

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Load filter options
    carregarOpcoesFilters();
});

// Load appointments for calendar
function carregarAgendamentos(fetchInfo, successCallback, failureCallback) {
    // Fazer requisição real para a API
    fetch('/api/agendamentos')
        .then(response => {
            if (!response.ok) {
                throw new Error('Erro ao carregar agendamentos');
            }
            return response.json();
        })
        .then(data => {
            successCallback(data);
        })
        .catch(error => {
            console.error('Erro ao carregar agendamentos:', error);
            SaudeFlex.notify.error('Erro ao carregar agendamentos do calendário');
            failureCallback(error);
        });
}

// Show appointment details
function mostrarDetalhesAgendamento(event) {
    agendamentoAtual = event;
    const props = event.extendedProps;

    const conteudo = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-calendar me-2 text-primary"></i>Informações do Agendamento</h6>
                <div class="mb-3">
                    <strong>Data/Hora:</strong><br>
                    <span class="text-muted">${moment(event.start).format('DD/MM/YYYY HH:mm')}</span>
                </div>
                <div class="mb-3">
                    <strong>Status:</strong><br>
                    <span class="badge bg-${getStatusColor(props.status)}">${props.status.charAt(0).toUpperCase() + props.status.slice(1)}</span>
                </div>
                <div class="mb-3">
                    <strong>Observações:</strong><br>
                    <span class="text-muted">${props.observacoes || 'Nenhuma observação'}</span>
                </div>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-user me-2 text-primary"></i>Cliente</h6>
                <div class="mb-3">
                    <strong>Nome:</strong><br>
                    <span class="text-muted">${props.cliente}</span>
                </div>
                <div class="mb-3">
                    <strong>Telefone:</strong><br>
                    <span class="text-muted">${props.telefone}</span>
                </div>

                <h6><i class="fas fa-box me-2 text-primary"></i>Produto</h6>
                <div class="mb-3">
                    <strong>Nome:</strong><br>
                    <span class="text-muted">${props.produto}</span>
                </div>

                <h6><i class="fas fa-user-tie me-2 text-primary"></i>Vendedor</h6>
                <div class="mb-3">
                    <strong>Responsável:</strong><br>
                    <span class="text-muted">${props.vendedor}</span>
                </div>
            </div>
        </div>
    `;

    document.getElementById('conteudo-agendamento').innerHTML = conteudo;

    // Update edit button
    document.getElementById('btn-editar-agendamento').onclick = function() {
        editarAgendamento(event.id);
    };

    const modal = new bootstrap.Modal(document.getElementById('modalAgendamento'));
    modal.show();
}

// Get status color for badges
function getStatusColor(status) {
    const colors = {
        'agendado': 'warning',
        'confirmado': 'primary',
        'realizado': 'success',
        'cancelado': 'danger'
    };
    return colors[status] || 'secondary';
}

// Open new appointment modal
function abrirModalNovoAgendamento(date) {
    // Set default date
    const dateStr = moment(date).format('YYYY-MM-DDTHH:mm');
    document.getElementById('data-agendamento').value = dateStr;

    const modal = new bootstrap.Modal(document.getElementById('modalNovoAgendamento'));
    modal.show();
}

// Load filter options
async function carregarOpcoesFilters() {
    try {
        // Carregar vendedores
        const vendedoresResponse = await fetch('/api/usuarios/vendedores');
        const vendedores = await vendedoresResponse.json();

        // Carregar produtos
        const produtosResponse = await fetch('/api/produtos');
        const produtos = await produtosResponse.json();

        // Carregar clientes
        const clientesResponse = await fetch('/api/clientes');
        const clientes = await clientesResponse.json();

        // Populate filter selects
        populateSelect('filtro-vendedor', vendedores);
        populateSelect('filtro-produto', produtos);

        // Populate modal selects
        populateSelect('vendedor-select', vendedores);
        populateSelect('produto-select', produtos);
        populateSelect('cliente-select', clientes);

    } catch (error) {
        console.error('Erro ao carregar opções:', error);
        // Fallback com dados simulados
        const vendedores = [
            { id: 1, nome: 'Ana Santos' },
            { id: 2, nome: 'Carlos Lima' },
            { id: 3, nome: 'Maria Silva' }
        ];

        const produtos = [
            { id: 1, nome: 'Óleo Essencial' },
            { id: 2, nome: 'Creme Relaxante' },
            { id: 3, nome: 'Chá Detox' }
        ];

        const clientes = [
            { id: 1, nome: 'João Silva' },
            { id: 2, nome: 'Maria Oliveira' },
            { id: 3, nome: 'Pedro Santos' }
        ];

        // Populate filter selects
        populateSelect('filtro-vendedor', vendedores);
        populateSelect('filtro-produto', produtos);

        // Populate modal selects
        populateSelect('vendedor-select', vendedores);
        populateSelect('produto-select', produtos);
        populateSelect('cliente-select', clientes);
    }
}

// Populate select element
function populateSelect(selectId, options) {
    const select = document.getElementById(selectId);
    options.forEach(option => {
        const optionEl = document.createElement('option');
        optionEl.value = option.id;
        optionEl.textContent = option.nome;
        select.appendChild(optionEl);
    });
}

// Filter functions
function aplicarFiltros() {
    const vendedor = document.getElementById('filtro-vendedor').value;
    const status = document.getElementById('filtro-status').value;
    const produto = document.getElementById('filtro-produto').value;

    console.log('Aplicando filtros:', { vendedor, status, produto });

    // Reload calendar with filters
    calendar.refetchEvents();

    SaudeFlex.notify.success('Filtros aplicados com sucesso!');
}

function limparFiltros() {
    document.getElementById('filtro-vendedor').value = '';
    document.getElementById('filtro-status').value = '';
    document.getElementById('filtro-produto').value = '';

    // Reload calendar without filters
    calendar.refetchEvents();

    SaudeFlex.notify.info('Filtros removidos');
}

// Calendar view functions
function mudarVisualizacao(view) {
    calendar.changeView(view);
}

function irParaHoje() {
    calendar.today();
}

// Save new appointment
async function salvarNovoAgendamento() {
    const form = document.getElementById('form-novo-agendamento');

    const cliente = document.getElementById('cliente-select').value;
    const produto = document.getElementById('produto-select').value;
    const vendedor = document.getElementById('vendedor-select').value;
    const dataAgendamento = document.getElementById('data-agendamento').value;
    const observacoes = document.getElementById('observacoes-agendamento').value;

    if (!cliente || !produto || !vendedor || !dataAgendamento) {
        SaudeFlex.notify.error('Por favor, preencha todos os campos obrigatórios');
        return;
    }

    try {
        // Fazer requisição para criar agendamento
        const response = await fetch('/agendamentos/novo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                cliente_id: cliente,
                produto_id: produto,
                vendedor_id: vendedor,
                data_agendamento: dataAgendamento,
                observacoes: observacoes
            })
        });

        const result = await response.json();

        if (result.success) {
            // Close modal and refresh calendar
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalNovoAgendamento'));
            modal.hide();

            calendar.refetchEvents();

            SaudeFlex.notify.success('Agendamento criado com sucesso!');

            // Reset form
            form.reset();
        } else {
            SaudeFlex.notify.error(result.message || 'Erro ao criar agendamento');
        }

    } catch (error) {
        console.error('Erro ao salvar agendamento:', error);
        SaudeFlex.notify.error('Erro ao salvar agendamento. Tente novamente.');
    }
}

// Edit appointment
function editarAgendamento(id) {
    console.log('Editando agendamento:', id);
    // Implement edit functionality
    SaudeFlex.notify.info('Funcionalidade de edição será implementada em breve');
}

// Change appointment status
function alterarStatusAgendamento(novoStatus) {
    if (!agendamentoAtual) return;

    const statusTexto = {
        'confirmado': 'confirmar',
        'realizado': 'marcar como realizado',
        'cancelado': 'cancelar'
    };

    SaudeFlex.modal.confirm(
        `Tem certeza que deseja ${statusTexto[novoStatus]} este agendamento?`,
        function() {
            console.log('Alterando status para:', novoStatus);

            // Update event in calendar
            agendamentoAtual.setProp('className', `fc-event-${novoStatus}`);
            agendamentoAtual.setExtendedProp('status', novoStatus);

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('modalAgendamento'));
            modal.hide();

            SaudeFlex.notify.success(`Agendamento ${statusTexto[novoStatus]} com sucesso!`);
        }
    );
}
</script>
{% endblock %}
