"""
Módulo de conexão e gerenciamento do banco de dados SQLite
Sistema Saúde Flex - Gestão de Agendamentos e Vendas
"""

import sqlite3
import os
import logging
from datetime import datetime
from config import AppConfig

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Gerenciador de conexões e operações do banco de dados SQLite
    """
    
    def __init__(self):
        """Inicializa o gerenciador do banco de dados"""
        self.db_path = AppConfig.DATABASE_PATH
        self._ensure_database_exists()
    
    def _ensure_database_exists(self):
        """Garante que o banco de dados existe"""
        if not os.path.exists(self.db_path):
            logger.info(f"Criando novo banco de dados: {self.db_path}")
            self.init_database()
    
    def get_connection(self):
        """
        Retorna uma nova conexão com o banco de dados
        
        Returns:
            sqlite3.Connection: Conexão com o banco
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Permite acesso por nome da coluna
        conn.execute("PRAGMA foreign_keys = ON")  # Habilita chaves estrangeiras
        return conn
    
    def init_database(self):
        """
        Inicializa o banco de dados criando todas as tabelas necessárias
        """
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Criar todas as tabelas
            self._create_usuarios_table(cursor)
            self._create_categorias_table(cursor)
            self._create_produtos_table(cursor)
            self._create_clientes_table(cursor)
            self._create_cliente_anexos_table(cursor)
            self._create_agendamentos_table(cursor)
            self._create_vendas_table(cursor)
            self._create_itens_venda_table(cursor)
            self._create_logs_auditoria_table(cursor)
            
            # Inserir dados iniciais
            self._insert_initial_data(cursor)
            
            conn.commit()
            logger.info("Banco de dados inicializado com sucesso!")
            
        except Exception as e:
            conn.rollback()
            logger.error(f"Erro ao inicializar banco de dados: {e}")
            raise
        finally:
            conn.close()
    
    def _create_usuarios_table(self, cursor):
        """Cria a tabela de usuários"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS usuarios (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                senha TEXT NOT NULL,
                tipo TEXT NOT NULL DEFAULT 'vendedor' CHECK (tipo IN ('admin', 'gerente', 'vendedor')),
                ativo INTEGER DEFAULT 1,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_ultimo_login TIMESTAMP,
                tentativas_login INTEGER DEFAULT 0
            )
        ''')
    
    def _create_categorias_table(self, cursor):
        """Cria a tabela de categorias"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categorias (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL UNIQUE,
                descricao TEXT,
                ativo INTEGER DEFAULT 1,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
    
    def _create_produtos_table(self, cursor):
        """Cria a tabela de produtos"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS produtos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                descricao TEXT,
                preco_custo REAL NOT NULL DEFAULT 0,
                lucro_desejado REAL NOT NULL DEFAULT 0,
                tipo_lucro TEXT NOT NULL DEFAULT 'percentual' CHECK (tipo_lucro IN ('percentual', 'valor')),
                preco_venda REAL NOT NULL DEFAULT 0,
                categoria_id INTEGER,
                estoque_atual INTEGER DEFAULT 0,
                estoque_minimo INTEGER DEFAULT 5,
                foto TEXT,
                ativo INTEGER DEFAULT 1,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_ultima_venda TIMESTAMP,
                FOREIGN KEY (categoria_id) REFERENCES categorias (id)
            )
        ''')
    
    def _create_clientes_table(self, cursor):
        """Cria a tabela de clientes"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clientes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nome TEXT NOT NULL,
                telefone TEXT NOT NULL,
                email TEXT,
                cpf TEXT,
                logradouro TEXT,
                numero TEXT,
                bairro TEXT,
                cidade TEXT,
                uf TEXT,
                cep TEXT,
                data_nascimento DATE,
                observacoes TEXT,
                ativo INTEGER DEFAULT 1,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_ultima_compra TIMESTAMP
            )
        ''')
    
    def _create_cliente_anexos_table(self, cursor):
        """Cria a tabela de anexos de clientes"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cliente_anexos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cliente_id INTEGER NOT NULL,
                nome_arquivo TEXT NOT NULL,
                nome_original TEXT NOT NULL,
                tipo_arquivo TEXT NOT NULL,
                tamanho INTEGER NOT NULL,
                data_upload TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (cliente_id) REFERENCES clientes (id) ON DELETE CASCADE
            )
        ''')
    
    def _create_agendamentos_table(self, cursor):
        """Cria a tabela de agendamentos"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agendamentos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cliente_id INTEGER NOT NULL,
                produto_id INTEGER NOT NULL,
                vendedor_id INTEGER NOT NULL,
                data_agendamento DATETIME NOT NULL,
                observacoes TEXT,
                status TEXT NOT NULL DEFAULT 'agendado' CHECK (status IN ('agendado', 'confirmado', 'realizado', 'cancelado')),
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (cliente_id) REFERENCES clientes (id),
                FOREIGN KEY (produto_id) REFERENCES produtos (id),
                FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
            )
        ''')
    
    def _create_vendas_table(self, cursor):
        """Cria a tabela de vendas"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vendas (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cliente_id INTEGER,
                vendedor_id INTEGER NOT NULL,
                subtotal REAL NOT NULL,
                desconto_total REAL DEFAULT 0,
                tipo_desconto_total TEXT DEFAULT 'valor' CHECK (tipo_desconto_total IN ('valor', 'percentual')),
                total_final REAL NOT NULL,
                observacoes TEXT,
                data_venda TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'finalizada' CHECK (status IN ('finalizada', 'cancelada')),
                FOREIGN KEY (cliente_id) REFERENCES clientes (id),
                FOREIGN KEY (vendedor_id) REFERENCES usuarios (id)
            )
        ''')
    
    def _create_itens_venda_table(self, cursor):
        """Cria a tabela de itens de venda"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS itens_venda (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                venda_id INTEGER NOT NULL,
                produto_id INTEGER NOT NULL,
                quantidade INTEGER NOT NULL,
                preco_unitario REAL NOT NULL,
                desconto_item REAL DEFAULT 0,
                tipo_desconto_item TEXT DEFAULT 'valor' CHECK (tipo_desconto_item IN ('valor', 'percentual')),
                subtotal_item REAL NOT NULL,
                FOREIGN KEY (venda_id) REFERENCES vendas (id) ON DELETE CASCADE,
                FOREIGN KEY (produto_id) REFERENCES produtos (id)
            )
        ''')
    
    def _create_logs_auditoria_table(self, cursor):
        """Cria a tabela de logs de auditoria"""
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS logs_auditoria (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                usuario_id INTEGER,
                acao TEXT NOT NULL,
                tabela TEXT NOT NULL,
                registro_id INTEGER,
                dados_anteriores TEXT,
                dados_novos TEXT,
                ip_address TEXT,
                user_agent TEXT,
                data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (usuario_id) REFERENCES usuarios (id)
            )
        ''')


    def _insert_initial_data(self, cursor):
        """Insere dados iniciais no banco de dados"""
        # Verificar se já existe usuário admin
        cursor.execute("SELECT id FROM usuarios WHERE email = '<EMAIL>'")
        if not cursor.fetchone():
            # Criar usuário admin padrão
            import bcrypt
            senha_hash = bcrypt.hashpw('admin123'.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            cursor.execute('''
                INSERT INTO usuarios (nome, email, senha, tipo)
                VALUES (?, ?, ?, ?)
            ''', ('Administrador do Sistema', '<EMAIL>', senha_hash, 'admin'))
            logger.info("Usuário admin criado: <EMAIL> / admin123")

        # Inserir categorias padrão
        categorias_padrao = [
            ('Suplementos', 'Suplementos alimentares e vitaminas'),
            ('Bem-estar', 'Produtos para bem-estar e relaxamento'),
            ('Fitness', 'Equipamentos e acessórios para exercícios'),
            ('Cuidados Pessoais', 'Produtos para cuidados com o corpo'),
            ('Terapias', 'Produtos para terapias alternativas')
        ]

        for nome, descricao in categorias_padrao:
            cursor.execute("SELECT id FROM categorias WHERE nome = ?", (nome,))
            if not cursor.fetchone():
                cursor.execute('''
                    INSERT INTO categorias (nome, descricao)
                    VALUES (?, ?)
                ''', (nome, descricao))

        logger.info("Dados iniciais inseridos com sucesso!")

    def execute_query(self, query, params=None):
        """
        Executa uma query no banco de dados

        Args:
            query (str): Query SQL a ser executada
            params (tuple, optional): Parâmetros da query

        Returns:
            list: Resultados da query
        """
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            if query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                return [dict(row) for row in results]
            else:
                conn.commit()
                return cursor.lastrowid

        except Exception as e:
            conn.rollback()
            logger.error(f"Erro ao executar query: {e}")
            raise
        finally:
            conn.close()

    def log_action(self, usuario_id, acao, tabela, registro_id=None, dados_anteriores=None, dados_novos=None, ip_address=None, user_agent=None):
        """
        Registra uma ação no log de auditoria

        Args:
            usuario_id (int): ID do usuário que executou a ação
            acao (str): Tipo de ação (CREATE, UPDATE, DELETE, LOGIN, etc.)
            tabela (str): Nome da tabela afetada
            registro_id (int, optional): ID do registro afetado
            dados_anteriores (str, optional): Dados antes da alteração (JSON)
            dados_novos (str, optional): Dados após a alteração (JSON)
            ip_address (str, optional): Endereço IP do usuário
            user_agent (str, optional): User agent do navegador
        """
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO logs_auditoria
                (usuario_id, acao, tabela, registro_id, dados_anteriores, dados_novos, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (usuario_id, acao, tabela, registro_id, dados_anteriores, dados_novos, ip_address, user_agent))

            conn.commit()

        except Exception as e:
            logger.error(f"Erro ao registrar log de auditoria: {e}")
        finally:
            conn.close()

    def backup_database(self, backup_path):
        """
        Cria um backup do banco de dados

        Args:
            backup_path (str): Caminho onde salvar o backup
        """
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            logger.info(f"Backup criado em: {backup_path}")
            return True
        except Exception as e:
            logger.error(f"Erro ao criar backup: {e}")
            return False

    def get_database_stats(self):
        """
        Retorna estatísticas do banco de dados

        Returns:
            dict: Estatísticas das tabelas
        """
        stats = {}
        tabelas = ['usuarios', 'categorias', 'produtos', 'clientes', 'agendamentos', 'vendas', 'itens_venda']

        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            for tabela in tabelas:
                cursor.execute(f"SELECT COUNT(*) as total FROM {tabela}")
                result = cursor.fetchone()
                stats[tabela] = result['total'] if result else 0

        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
        finally:
            conn.close()

        return stats


# Instância global do gerenciador de banco
db = DatabaseManager()
