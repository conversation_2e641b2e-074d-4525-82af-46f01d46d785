"""
Modelos de dados do sistema Saúde Flex
Contém as classes que representam as entidades do negócio
"""

from database import db
import bcrypt


class Usuario:
    """
    Classe que representa um usuário do sistema

    Attributes:
        id (int): Identificador único do usuário
        nome (str): Nome completo do usuário
        email (str): Email para login
        senha (str): <PERSON>h da senha
        tipo (str): <PERSON><PERSON><PERSON> de usu<PERSON>rio (admin, gerente, vendedor)
        ativo (bool): Status ativo/inativo
    """

    def __init__(self, id=None, nome=None, email=None, senha=None, tipo=None, ativo=True):
        """
        Inicializa uma instância de usuário

        Args:
            id (int, optional): ID do usuário
            nome (str, optional): Nome do usuário
            email (str, optional): Email do usuário
            senha (str, optional): Hash da senha
            tipo (str, optional): Tipo do usuário
            ativo (bool, optional): Status ativo. Defaults to True.
        """
        self.id = id
        self.nome = nome
        self.email = email
        self.senha = senha
        self.tipo = tipo
        self.ativo = ativo

    @staticmethod
    def criar(nome, email, senha, tipo):
        """
        Cria um novo usuário no sistema

        Args:
            nome (str): Nome completo do usuário
            email (str): Email para login
            senha (str): Senha em texto plano (será criptografada)
            tipo (str): Tipo de usuário

        Returns:
            int: ID do usuário criado

        Raises:
            Exception: Se erro ao criar usuário
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            # Criptografar senha usando bcrypt
            senha_hash = Usuario._criptografar_senha(senha)

            cursor.execute('''
                INSERT INTO usuarios (nome, email, senha, tipo)
                VALUES (?, ?, ?, ?)
            ''', (nome, email, senha_hash, tipo))

            user_id = cursor.lastrowid
            conn.commit()

            return user_id

        finally:
            conn.close()

    @staticmethod
    def _criptografar_senha(senha):
        """
        Criptografa uma senha usando bcrypt

        Args:
            senha (str): Senha em texto plano

        Returns:
            str: Hash da senha
        """
        return bcrypt.hashpw(senha.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    @staticmethod
    def buscar_por_email(email):
        """
        Busca usuário pelo endereço de email

        Args:
            email (str): Email do usuário

        Returns:
            Usuario: Instância do usuário encontrado ou None
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT * FROM usuarios WHERE email = ? AND ativo = 1', (email,))
            row = cursor.fetchone()

            if row:
                return Usuario._criar_instancia_de_row(row)

            return None

        finally:
            conn.close()

    @staticmethod
    def buscar_por_id(user_id):
        """
        Busca usuário pelo ID

        Args:
            user_id (int): ID do usuário

        Returns:
            Usuario: Instância do usuário encontrado ou None
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT * FROM usuarios WHERE id = ? AND ativo = 1', (user_id,))
            row = cursor.fetchone()

            if row:
                return Usuario._criar_instancia_de_row(row)

            return None

        finally:
            conn.close()

    @staticmethod
    def _criar_instancia_de_row(row):
        """
        Cria instância de Usuario a partir de uma linha do banco

        Args:
            row: Linha retornada do banco de dados

        Returns:
            Usuario: Nova instância de usuário
        """
        return Usuario(
            id=row['id'],
            nome=row['nome'],
            email=row['email'],
            senha=row['senha'],
            tipo=row['tipo'],
            ativo=row['ativo']
        )
    
    @staticmethod
    def listar_vendedores():
        """
        Lista todos os usuários que podem realizar vendas

        Returns:
            list: Lista de dicionários com id e nome dos vendedores
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT id, nome FROM usuarios
                WHERE tipo IN ('vendedor', 'gerente', 'admin') AND ativo = 1
                ORDER BY nome
            ''')

            vendedores = cursor.fetchall()
            return [{'id': row['id'], 'nome': row['nome']} for row in vendedores]

        finally:
            conn.close()

    def verificar_senha(self, senha):
        """
        Verifica se a senha fornecida corresponde à senha do usuário

        Args:
            senha (str): Senha em texto plano para verificação

        Returns:
            bool: True se a senha estiver correta, False caso contrário
        """
        return bcrypt.checkpw(senha.encode('utf-8'), self.senha.encode('utf-8'))

class Produto:
    """
    Classe que representa um produto do sistema

    Gerencia operações relacionadas aos produtos como criação,
    busca, listagem e cálculo de preços
    """

    @staticmethod
    def criar(nome, descricao, preco_custo, lucro_desejado, tipo_lucro,
              categoria_id, estoque_atual, estoque_minimo, foto=None):
        """
        Cria um novo produto no sistema

        Args:
            nome (str): Nome do produto
            descricao (str): Descrição detalhada
            preco_custo (float): Preço de custo
            lucro_desejado (float): Lucro desejado (valor ou percentual)
            tipo_lucro (str): Tipo do lucro ('percentual' ou 'valor')
            categoria_id (int): ID da categoria
            estoque_atual (int): Quantidade em estoque
            estoque_minimo (int): Estoque mínimo para alerta
            foto (str, optional): Caminho da foto do produto

        Returns:
            int: ID do produto criado
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            # Calcular preço de venda baseado no tipo de lucro
            preco_venda = Produto._calcular_preco_venda(preco_custo, lucro_desejado, tipo_lucro)

            cursor.execute('''
                INSERT INTO produtos (nome, descricao, preco_custo, lucro_desejado, tipo_lucro,
                                    preco_venda, categoria_id, estoque_atual, estoque_minimo, foto)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (nome, descricao, preco_custo, lucro_desejado, tipo_lucro,
                  preco_venda, categoria_id, estoque_atual, estoque_minimo, foto))

            produto_id = cursor.lastrowid
            conn.commit()

            return produto_id

        finally:
            conn.close()

    @staticmethod
    def _calcular_preco_venda(preco_custo, lucro_desejado, tipo_lucro):
        """
        Calcula o preço de venda baseado no custo e lucro

        Args:
            preco_custo (float): Preço de custo
            lucro_desejado (float): Lucro desejado
            tipo_lucro (str): Tipo do lucro ('percentual' ou 'valor')

        Returns:
            float: Preço de venda calculado
        """
        if tipo_lucro == 'percentual':
            return preco_custo * (1 + lucro_desejado / 100)
        else:
            return preco_custo + lucro_desejado
    
    @staticmethod
    def listar():
        """
        Lista todos os produtos ativos do sistema

        Returns:
            list: Lista de dicionários com dados dos produtos
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT p.*, c.nome as categoria_nome
                FROM produtos p
                LEFT JOIN categorias c ON p.categoria_id = c.id
                WHERE p.ativo = 1
                ORDER BY p.nome
            ''')

            produtos = cursor.fetchall()
            return [dict(row) for row in produtos]

        finally:
            conn.close()

    @staticmethod
    def buscar_por_id(produto_id):
        """
        Busca produto pelo ID

        Args:
            produto_id (int): ID do produto

        Returns:
            dict: Dados do produto encontrado ou None
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT p.*, c.nome as categoria_nome
                FROM produtos p
                LEFT JOIN categorias c ON p.categoria_id = c.id
                WHERE p.id = ? AND p.ativo = 1
            ''', (produto_id,))

            row = cursor.fetchone()
            return dict(row) if row else None

        finally:
            conn.close()

class Cliente:
    """
    Classe que representa um cliente do sistema

    Gerencia operações relacionadas aos clientes como criação,
    busca e listagem
    """

    @staticmethod
    def criar(nome, telefone, email=None, logradouro=None, numero=None,
              bairro=None, cidade=None, uf=None, cep=None):
        """
        Cria um novo cliente no sistema

        Args:
            nome (str): Nome completo do cliente
            telefone (str): Telefone de contato
            email (str, optional): Email do cliente
            logradouro (str, optional): Endereço - logradouro
            numero (str, optional): Endereço - número
            bairro (str, optional): Endereço - bairro
            cidade (str, optional): Endereço - cidade
            uf (str, optional): Endereço - UF
            cep (str, optional): Endereço - CEP

        Returns:
            int: ID do cliente criado
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO clientes (nome, telefone, email, logradouro, numero, bairro, cidade, uf, cep)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (nome, telefone, email, logradouro, numero, bairro, cidade, uf, cep))

            cliente_id = cursor.lastrowid
            conn.commit()

            return cliente_id

        finally:
            conn.close()
    
    @staticmethod
    def listar():
        """
        Lista todos os clientes cadastrados

        Returns:
            list: Lista de dicionários com dados dos clientes
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT * FROM clientes ORDER BY nome')
            clientes = cursor.fetchall()
            return [dict(row) for row in clientes]

        finally:
            conn.close()

    @staticmethod
    def buscar_por_id(cliente_id):
        """
        Busca cliente pelo ID

        Args:
            cliente_id (int): ID do cliente

        Returns:
            dict: Dados do cliente encontrado ou None
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT * FROM clientes WHERE id = ?', (cliente_id,))
            row = cursor.fetchone()
            return dict(row) if row else None

        finally:
            conn.close()

class Agendamento:
    """
    Classe que representa um agendamento do sistema

    Gerencia operações relacionadas aos agendamentos como criação,
    busca, listagem e alteração de status
    """

    @staticmethod
    def criar(cliente_id, produto_id, vendedor_id, data_agendamento, observacoes=None):
        """
        Cria um novo agendamento no sistema

        Args:
            cliente_id (int): ID do cliente
            produto_id (int): ID do produto
            vendedor_id (int): ID do vendedor responsável
            data_agendamento (str): Data e hora do agendamento
            observacoes (str, optional): Observações sobre o agendamento

        Returns:
            int: ID do agendamento criado
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO agendamentos (cliente_id, produto_id, vendedor_id, data_agendamento, observacoes)
                VALUES (?, ?, ?, ?, ?)
            ''', (cliente_id, produto_id, vendedor_id, data_agendamento, observacoes))

            agendamento_id = cursor.lastrowid
            conn.commit()

            return agendamento_id

        finally:
            conn.close()
    
    @staticmethod
    def listar():
        """
        Lista todos os agendamentos com informações relacionadas

        Returns:
            list: Lista de dicionários com dados dos agendamentos
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT a.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
                       p.nome as produto_nome, u.nome as vendedor_nome
                FROM agendamentos a
                JOIN clientes c ON a.cliente_id = c.id
                JOIN produtos p ON a.produto_id = p.id
                JOIN usuarios u ON a.vendedor_id = u.id
                ORDER BY a.data_agendamento DESC
            ''')

            agendamentos = cursor.fetchall()
            return [dict(row) for row in agendamentos]

        finally:
            conn.close()

class Venda:
    """
    Classe que representa uma venda do sistema

    Gerencia operações relacionadas às vendas como criação,
    busca, listagem e cálculos de totais
    """

    @staticmethod
    def criar(cliente_id, vendedor_id, itens, desconto_total=0, observacoes=None):
        """
        Cria uma nova venda com seus itens

        Args:
            cliente_id (int): ID do cliente
            vendedor_id (int): ID do vendedor
            itens (list): Lista de itens da venda
            desconto_total (float, optional): Desconto total da venda
            observacoes (str, optional): Observações sobre a venda

        Returns:
            int: ID da venda criada

        Raises:
            Exception: Se erro durante a criação da venda
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            # Validar e calcular totais
            subtotal = Venda._validar_e_calcular_subtotal(itens)
            desconto_itens = sum(item.get('desconto_item', 0) for item in itens)
            total_final = subtotal - desconto_itens - desconto_total

            # Inserir venda principal
            venda_id = Venda._inserir_venda_principal(
                cursor, cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes
            )

            # Processar itens da venda
            Venda._processar_itens_venda(cursor, venda_id, itens)

            conn.commit()
            return venda_id

        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()

    @staticmethod
    def _validar_e_calcular_subtotal(itens):
        """
        Valida itens e calcula subtotal da venda

        Args:
            itens (list): Lista de itens da venda

        Returns:
            float: Subtotal calculado

        Raises:
            Exception: Se produto não encontrado ou estoque insuficiente
        """
        subtotal = 0
        for item in itens:
            produto = Produto.buscar_por_id(item['produto_id'])
            if not produto:
                raise Exception(f"Produto {item['produto_id']} não encontrado")

            # Verificar estoque disponível
            if produto['estoque_atual'] < item['quantidade']:
                raise Exception(
                    f"Estoque insuficiente para {produto['nome']}. "
                    f"Disponível: {produto['estoque_atual']}"
                )

            subtotal += item['preco_unitario'] * item['quantidade']

        return subtotal

    @staticmethod
    def _inserir_venda_principal(cursor, cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes):
        """
        Insere o registro principal da venda

        Args:
            cursor: Cursor do banco de dados
            cliente_id (int): ID do cliente
            vendedor_id (int): ID do vendedor
            subtotal (float): Subtotal da venda
            desconto_total (float): Desconto total
            total_final (float): Total final
            observacoes (str): Observações

        Returns:
            int: ID da venda criada
        """
        cursor.execute('''
            INSERT INTO vendas (cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (cliente_id, vendedor_id, subtotal, desconto_total, total_final, observacoes))

        return cursor.lastrowid

    @staticmethod
    def _processar_itens_venda(cursor, venda_id, itens):
        """
        Processa os itens da venda (inserção e atualização de estoque)

        Args:
            cursor: Cursor do banco de dados
            venda_id (int): ID da venda
            itens (list): Lista de itens da venda
        """
        for item in itens:
            subtotal_item = (item['preco_unitario'] * item['quantidade']) - item.get('desconto_item', 0)

            # Inserir item da venda
            cursor.execute('''
                INSERT INTO itens_venda (venda_id, produto_id, quantidade, preco_unitario, desconto_item, subtotal_item)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (venda_id, item['produto_id'], item['quantidade'],
                  item['preco_unitario'], item.get('desconto_item', 0), subtotal_item))

            # Atualizar estoque do produto
            cursor.execute('''
                UPDATE produtos SET estoque_atual = estoque_atual - ?
                WHERE id = ?
            ''', (item['quantidade'], item['produto_id']))

    @staticmethod
    def listar():
        """
        Lista todas as vendas com informações relacionadas

        Returns:
            list: Lista de dicionários com dados das vendas
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
                       u.nome as vendedor_nome
                FROM vendas v
                JOIN clientes c ON v.cliente_id = c.id
                JOIN usuarios u ON v.vendedor_id = u.id
                ORDER BY v.data_venda DESC
            ''')

            vendas = cursor.fetchall()
            return [dict(row) for row in vendas]

        finally:
            conn.close()

    @staticmethod
    def buscar_por_id(venda_id):
        """
        Busca venda por ID com seus itens

        Args:
            venda_id (int): ID da venda

        Returns:
            dict: Dados da venda com itens ou None se não encontrada
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            # Buscar dados principais da venda
            venda = Venda._buscar_dados_venda(cursor, venda_id)
            if not venda:
                return None

            # Buscar itens da venda
            itens = Venda._buscar_itens_venda(cursor, venda_id)
            venda['itens'] = itens

            return venda

        finally:
            conn.close()

    @staticmethod
    def _buscar_dados_venda(cursor, venda_id):
        """
        Busca dados principais da venda

        Args:
            cursor: Cursor do banco de dados
            venda_id (int): ID da venda

        Returns:
            dict: Dados da venda ou None
        """
        cursor.execute('''
            SELECT v.*, c.nome as cliente_nome, c.telefone as cliente_telefone,
                   c.email as cliente_email, u.nome as vendedor_nome
            FROM vendas v
            JOIN clientes c ON v.cliente_id = c.id
            JOIN usuarios u ON v.vendedor_id = u.id
            WHERE v.id = ?
        ''', (venda_id,))

        venda = cursor.fetchone()
        return dict(venda) if venda else None

    @staticmethod
    def _buscar_itens_venda(cursor, venda_id):
        """
        Busca itens de uma venda

        Args:
            cursor: Cursor do banco de dados
            venda_id (int): ID da venda

        Returns:
            list: Lista de itens da venda
        """
        cursor.execute('''
            SELECT iv.*, p.nome as produto_nome
            FROM itens_venda iv
            JOIN produtos p ON iv.produto_id = p.id
            WHERE iv.venda_id = ?
        ''', (venda_id,))

        return [dict(row) for row in cursor.fetchall()]

class Categoria:
    """
    Classe que representa uma categoria de produtos

    Gerencia operações relacionadas às categorias de produtos
    """

    @staticmethod
    def listar():
        """
        Lista todas as categorias ativas

        Returns:
            list: Lista de dicionários com dados das categorias
        """
        conn = db.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('SELECT * FROM categorias WHERE ativo = 1 ORDER BY nome')
            categorias = cursor.fetchall()
            return [dict(row) for row in categorias]

        finally:
            conn.close()
