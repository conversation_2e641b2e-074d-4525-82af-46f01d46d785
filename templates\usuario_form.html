<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Usuário - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .permission-info {
            background-color: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{{ url_for('index') }}">
                <i class="fas fa-heartbeat me-2"></i>Saúde Flex
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('produtos') }}">
                            <i class="fas fa-box me-1"></i>Produtos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('clientes') }}">
                            <i class="fas fa-users me-1"></i>Clientes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('vendas') }}">
                            <i class="fas fa-shopping-cart me-1"></i>Vendas
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ session.user_nome or 'Usuário' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i>Sair
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Novo Usuário</h1>
                <p class="text-muted">Cadastrar novo usuário no sistema</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('usuarios') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar
                </a>
            </div>
        </div>

        <!-- Formulário -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user-plus me-2"></i>Informações do Usuário
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="nome" class="form-label">Nome Completo *</label>
                                    <input type="text" class="form-control" id="nome" name="nome" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="tipo" class="form-label">Tipo de Usuário *</label>
                                    <select class="form-select" id="tipo" name="tipo" required onchange="showPermissions()">
                                        <option value="">Selecione o tipo</option>
                                        <option value="vendedor">Vendedor</option>
                                        <option value="gerente">Gerente</option>
                                        <option value="admin">Administrador</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="<EMAIL>" required>
                                <div class="form-text">Este email será usado para fazer login no sistema</div>
                            </div>

                            <div class="mb-3">
                                <label for="senha" class="form-label">Senha *</label>
                                <input type="password" class="form-control" id="senha" name="senha" 
                                       minlength="6" required>
                                <div class="form-text">Mínimo de 6 caracteres</div>
                            </div>

                            <!-- Informações de Permissões -->
                            <div id="permission-info" class="permission-info" style="display: none;">
                                <h6><i class="fas fa-shield-alt me-2"></i>Permissões do Tipo Selecionado</h6>
                                <div id="permission-details"></div>
                            </div>

                            <div class="d-flex justify-content-end mt-4">
                                <a href="{{ url_for('usuarios') }}" class="btn btn-secondary me-2">Cancelar</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>Salvar Usuário
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>Tipos de Usuário
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong class="text-danger">👑 Administrador</strong>
                            <p class="small text-muted mb-2">Acesso total ao sistema</p>
                            <ul class="small text-muted">
                                <li>Gerencia usuários</li>
                                <li>Gerencia produtos</li>
                                <li>Gerencia vendas</li>
                                <li>Gerencia agendamentos</li>
                                <li>Acesso a relatórios</li>
                            </ul>
                        </div>
                        
                        <div class="mb-3">
                            <strong class="text-warning">👔 Gerente</strong>
                            <p class="small text-muted mb-2">Gerencia operações</p>
                            <ul class="small text-muted">
                                <li>Gerencia produtos</li>
                                <li>Gerencia vendas</li>
                                <li>Gerencia agendamentos</li>
                                <li>Gerencia clientes</li>
                            </ul>
                        </div>
                        
                        <div class="mb-3">
                            <strong class="text-success">🛒 Vendedor</strong>
                            <p class="small text-muted mb-2">Foco em vendas</p>
                            <ul class="small text-muted">
                                <li>Realizar vendas</li>
                                <li>Criar agendamentos</li>
                                <li>Cadastrar clientes</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showPermissions() {
            const tipo = document.getElementById('tipo').value;
            const permissionInfo = document.getElementById('permission-info');
            const permissionDetails = document.getElementById('permission-details');
            
            if (!tipo) {
                permissionInfo.style.display = 'none';
                return;
            }
            
            let details = '';
            
            switch(tipo) {
                case 'admin':
                    details = `
                        <strong class="text-danger">👑 Administrador - Acesso Total</strong>
                        <ul class="mt-2 mb-0">
                            <li>Gerenciar usuários (criar, editar, excluir)</li>
                            <li>Gerenciar produtos e categorias</li>
                            <li>Gerenciar vendas e agendamentos</li>
                            <li>Acesso completo a relatórios</li>
                            <li>Configurações do sistema</li>
                        </ul>
                    `;
                    break;
                case 'gerente':
                    details = `
                        <strong class="text-warning">👔 Gerente - Operações</strong>
                        <ul class="mt-2 mb-0">
                            <li>Gerenciar produtos e categorias</li>
                            <li>Gerenciar vendas e agendamentos</li>
                            <li>Gerenciar clientes</li>
                            <li>Visualizar relatórios operacionais</li>
                        </ul>
                    `;
                    break;
                case 'vendedor':
                    details = `
                        <strong class="text-success">🛒 Vendedor - Vendas</strong>
                        <ul class="mt-2 mb-0">
                            <li>Realizar vendas</li>
                            <li>Criar e gerenciar agendamentos</li>
                            <li>Cadastrar novos clientes</li>
                            <li>Visualizar próprias vendas</li>
                        </ul>
                    `;
                    break;
            }
            
            permissionDetails.innerHTML = details;
            permissionInfo.style.display = 'block';
        }
    </script>
</body>
</html>
