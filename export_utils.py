"""
Utilitários para exportação de relatórios
Módulo responsável por gerar arquivos PDF e Excel dos relatórios do sistema
"""

import os
import io
from datetime import datetime
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.units import inch
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from flask import make_response
from config import AppConfig


class ExportadorRelatorios:
    """
    Classe responsável pela exportação de relatórios em diferentes formatos
    """
    
    def __init__(self):
        """Inicializa o exportador com configurações padrão"""
        self.styles = getSampleStyleSheet()
        self._criar_estilos_customizados()
    
    def _criar_estilos_customizados(self):
        """Cria estilos customizados para os relatórios"""
        # Estilo para título principal
        self.styles.add(ParagraphStyle(
            name='TituloPrincipal',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # Centralizado
            textColor=colors.HexColor('#667eea')
        ))
        
        # Estilo para subtítulos
        self.styles.add(ParagraphStyle(
            name='Subtitulo',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=12,
            textColor=colors.HexColor('#495057')
        ))
    
    def exportar_vendas_periodo_pdf(self, vendas, data_inicio, data_fim, estatisticas):
        """
        Exporta relatório de vendas por período em PDF
        
        Args:
            vendas (list): Lista de vendas
            data_inicio (str): Data de início do período
            data_fim (str): Data de fim do período
            estatisticas (dict): Estatísticas do período
            
        Returns:
            Response: Arquivo PDF para download
        """
        # Criar buffer em memória
        buffer = io.BytesIO()
        
        # Criar documento PDF
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []
        
        # Título
        titulo = Paragraph("Relatório de Vendas por Período", self.styles['TituloPrincipal'])
        story.append(titulo)
        
        # Período
        periodo = Paragraph(f"Período: {data_inicio} a {data_fim}", self.styles['Subtitulo'])
        story.append(periodo)
        story.append(Spacer(1, 12))
        
        # Resumo estatístico
        resumo_data = [
            ['Métrica', 'Valor'],
            ['Total de Vendas', f"R$ {estatisticas.get('total_vendas', 0):.2f}"],
            ['Quantidade de Vendas', str(estatisticas.get('qtd_vendas', 0))],
            ['Ticket Médio', f"R$ {estatisticas.get('ticket_medio', 0):.2f}"],
            ['Total de Descontos', f"R$ {estatisticas.get('total_descontos', 0):.2f}"]
        ]
        
        resumo_table = Table(resumo_data, colWidths=[3*inch, 2*inch])
        resumo_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(resumo_table)
        story.append(Spacer(1, 20))
        
        # Tabela de vendas
        if vendas:
            vendas_titulo = Paragraph("Detalhamento das Vendas", self.styles['Subtitulo'])
            story.append(vendas_titulo)
            
            # Cabeçalho da tabela
            vendas_data = [['Data', 'Cliente', 'Vendedor', 'Total', 'Desconto', 'Total Final']]
            
            # Dados das vendas
            for venda in vendas:
                vendas_data.append([
                    venda.get('data_venda', ''),
                    venda.get('cliente_nome', ''),
                    venda.get('vendedor_nome', ''),
                    f"R$ {venda.get('subtotal', 0):.2f}",
                    f"R$ {venda.get('desconto_total', 0):.2f}",
                    f"R$ {venda.get('total_final', 0):.2f}"
                ])
            
            vendas_table = Table(vendas_data, colWidths=[1.2*inch, 1.5*inch, 1.2*inch, 1*inch, 1*inch, 1*inch])
            vendas_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#667eea')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(vendas_table)
        
        # Rodapé
        story.append(Spacer(1, 30))
        rodape = Paragraph(
            f"Relatório gerado em {datetime.now().strftime('%d/%m/%Y às %H:%M')} - Saúde Flex",
            self.styles['Normal']
        )
        story.append(rodape)
        
        # Gerar PDF
        doc.build(story)
        buffer.seek(0)
        
        # Criar resposta
        response = make_response(buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=vendas_periodo_{data_inicio}_{data_fim}.pdf'
        
        return response
    
    def exportar_vendas_periodo_excel(self, vendas, data_inicio, data_fim, estatisticas):
        """
        Exporta relatório de vendas por período em Excel
        
        Args:
            vendas (list): Lista de vendas
            data_inicio (str): Data de início do período
            data_fim (str): Data de fim do período
            estatisticas (dict): Estatísticas do período
            
        Returns:
            Response: Arquivo Excel para download
        """
        # Criar workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Vendas por Período"
        
        # Estilos
        titulo_font = Font(name='Arial', size=16, bold=True, color='FFFFFF')
        titulo_fill = PatternFill(start_color='667EEA', end_color='667EEA', fill_type='solid')
        header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='495057', end_color='495057', fill_type='solid')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Título
        ws.merge_cells('A1:F1')
        ws['A1'] = f"Relatório de Vendas - Período: {data_inicio} a {data_fim}"
        ws['A1'].font = titulo_font
        ws['A1'].fill = titulo_fill
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
        
        # Resumo estatístico
        ws['A3'] = "RESUMO ESTATÍSTICO"
        ws['A3'].font = header_font
        ws['A3'].fill = header_fill
        ws.merge_cells('A3:B3')
        
        resumo_data = [
            ['Total de Vendas', f"R$ {estatisticas.get('total_vendas', 0):.2f}"],
            ['Quantidade de Vendas', str(estatisticas.get('qtd_vendas', 0))],
            ['Ticket Médio', f"R$ {estatisticas.get('ticket_medio', 0):.2f}"],
            ['Total de Descontos', f"R$ {estatisticas.get('total_descontos', 0):.2f}"]
        ]
        
        row = 4
        for item in resumo_data:
            ws[f'A{row}'] = item[0]
            ws[f'B{row}'] = item[1]
            ws[f'A{row}'].border = border
            ws[f'B{row}'].border = border
            row += 1
        
        # Cabeçalho da tabela de vendas
        row += 2
        headers = ['Data', 'Cliente', 'Vendedor', 'Total', 'Desconto', 'Total Final']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center')
        
        # Dados das vendas
        row += 1
        for venda in vendas:
            ws.cell(row=row, column=1, value=venda.get('data_venda', '')).border = border
            ws.cell(row=row, column=2, value=venda.get('cliente_nome', '')).border = border
            ws.cell(row=row, column=3, value=venda.get('vendedor_nome', '')).border = border
            ws.cell(row=row, column=4, value=f"R$ {venda.get('subtotal', 0):.2f}").border = border
            ws.cell(row=row, column=5, value=f"R$ {venda.get('desconto_total', 0):.2f}").border = border
            ws.cell(row=row, column=6, value=f"R$ {venda.get('total_final', 0):.2f}").border = border
            row += 1
        
        # Ajustar largura das colunas
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Salvar em buffer
        buffer = io.BytesIO()
        wb.save(buffer)
        buffer.seek(0)
        
        # Criar resposta
        response = make_response(buffer.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=vendas_periodo_{data_inicio}_{data_fim}.xlsx'
        
        return response
    
    def exportar_produtos_mais_vendidos_pdf(self, produtos, periodo):
        """
        Exporta relatório de produtos mais vendidos em PDF
        
        Args:
            produtos (list): Lista de produtos mais vendidos
            periodo (str): Período do relatório
            
        Returns:
            Response: Arquivo PDF para download
        """
        # Implementação similar ao método de vendas por período
        # Será implementada conforme necessário
        pass
    
    def exportar_estoque_baixo_pdf(self, produtos):
        """
        Exporta relatório de estoque baixo em PDF
        
        Args:
            produtos (list): Lista de produtos com estoque baixo
            
        Returns:
            Response: Arquivo PDF para download
        """
        # Implementação similar ao método de vendas por período
        # Será implementada conforme necessário
        pass
