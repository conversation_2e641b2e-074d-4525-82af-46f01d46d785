<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .relatorio-card {
            height: 100%;
            cursor: pointer;
        }
        .relatorio-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 20px;
        }
        .icon-estoque { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); }
        .icon-vendas { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .icon-produtos { background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); }
        .icon-vendedores { background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); }
        .icon-lucro { background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%); }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Relatórios</h1>
                <p class="text-muted">Análises e relatórios estratégicos do sistema</p>
            </div>
        </div>

        <!-- Cards de Relatórios -->
        <div class="row">
            <!-- Estoque Baixo -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card relatorio-card" onclick="window.location.href='{{ url_for('relatorio_estoque_baixo') }}'">
                    <div class="card-body text-center">
                        <div class="relatorio-icon icon-estoque">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h5 class="card-title">Estoque Baixo</h5>
                        <p class="card-text text-muted">
                            Produtos com estoque abaixo do mínimo configurado. 
                            Identifique rapidamente itens que precisam ser repostos.
                        </p>
                        <div class="d-flex justify-content-between text-small">
                            <span><i class="fas fa-filter me-1"></i>Filtros por categoria</span>
                            <span><i class="fas fa-sort me-1"></i>Ordenação inteligente</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vendas por Período -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card relatorio-card" onclick="window.location.href='{{ url_for('relatorio_vendas_periodo') }}'">
                    <div class="card-body text-center">
                        <div class="relatorio-icon icon-vendas">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h5 class="card-title">Vendas por Período</h5>
                        <p class="card-text text-muted">
                            Análise detalhada das vendas em períodos específicos. 
                            Acompanhe o desempenho financeiro da empresa.
                        </p>
                        <div class="d-flex justify-content-between text-small">
                            <span><i class="fas fa-calendar me-1"></i>Filtro por data</span>
                            <span><i class="fas fa-file-pdf me-1"></i>Exportar PDF</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Produtos Mais Vendidos -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card relatorio-card" onclick="window.location.href='{{ url_for('relatorio_produtos_mais_vendidos') }}'">
                    <div class="card-body text-center">
                        <div class="relatorio-icon icon-produtos">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <h5 class="card-title">Produtos Mais Vendidos</h5>
                        <p class="card-text text-muted">
                            Ranking dos produtos com maior giro. 
                            Identifique os campeões de venda para estratégias futuras.
                        </p>
                        <div class="d-flex justify-content-between text-small">
                            <span><i class="fas fa-chart-pie me-1"></i>Gráficos visuais</span>
                            <span><i class="fas fa-medal me-1"></i>Top 20 produtos</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vendas por Vendedor -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card relatorio-card" onclick="window.location.href='{{ url_for('relatorio_vendas_por_vendedor') }}'">
                    <div class="card-body text-center">
                        <div class="relatorio-icon icon-vendedores">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="card-title">Desempenho por Vendedor</h5>
                        <p class="card-text text-muted">
                            Avalie o desempenho individual da equipe de vendas. 
                            Compare resultados e identifique oportunidades.
                        </p>
                        <div class="d-flex justify-content-between text-small">
                            <span><i class="fas fa-chart-bar me-1"></i>Comparativo</span>
                            <span><i class="fas fa-calculator me-1"></i>Ticket médio</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Análise de Lucro -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card relatorio-card" onclick="alert('Relatório em desenvolvimento!')">
                    <div class="card-body text-center">
                        <div class="relatorio-icon icon-lucro">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <h5 class="card-title">Análise de Lucro</h5>
                        <p class="card-text text-muted">
                            Análise detalhada do lucro por venda e produto. 
                            Compare custos, receitas e margens de lucro.
                        </p>
                        <div class="d-flex justify-content-between text-small">
                            <span><i class="fas fa-percentage me-1"></i>Margem de lucro</span>
                            <span><i class="fas fa-trending-up me-1"></i>Tendências</span>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-warning">Em Breve</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Relatório Personalizado -->
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card relatorio-card" onclick="alert('Funcionalidade em desenvolvimento!')">
                    <div class="card-body text-center">
                        <div class="relatorio-icon" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h5 class="card-title">Relatório Personalizado</h5>
                        <p class="card-text text-muted">
                            Crie relatórios personalizados com os dados que você precisa. 
                            Flexibilidade total para suas análises.
                        </p>
                        <div class="d-flex justify-content-between text-small">
                            <span><i class="fas fa-sliders-h me-1"></i>Personalizável</span>
                            <span><i class="fas fa-save me-1"></i>Salvar modelos</span>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-secondary">Futuro</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações Adicionais -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>Sobre os Relatórios
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">📊 Recursos Disponíveis</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>Filtros avançados por período</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Exportação para PDF</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Gráficos interativos</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Dados em tempo real</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">🎯 Benefícios</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-lightbulb text-warning me-2"></i>Tomada de decisão baseada em dados</li>
                                    <li><i class="fas fa-lightbulb text-warning me-2"></i>Identificação de oportunidades</li>
                                    <li><i class="fas fa-lightbulb text-warning me-2"></i>Controle de estoque eficiente</li>
                                    <li><i class="fas fa-lightbulb text-warning me-2"></i>Avaliação de desempenho da equipe</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
