<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Produto - Saúde Flex</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .price-display {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            margin-top: 10px;
        }
        .price-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    {% include 'base_navbar.html' %}

    <!-- Conteúdo Principal -->
    <div class="container mt-4">
        <!-- Alertas -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Cabeçalho -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Novo Produto</h1>
                <p class="text-muted">Cadastrar novo produto no sistema</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('produtos') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Voltar
                </a>
            </div>
        </div>

        <!-- Formulário -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-box me-2"></i>Informações do Produto
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="produtoForm">
                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="nome" class="form-label">Nome do Produto *</label>
                                    <input type="text" class="form-control" id="nome" name="nome" required>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="categoria_id" class="form-label">Categoria</label>
                                    <select class="form-select" id="categoria_id" name="categoria_id">
                                        <option value="">Selecione uma categoria</option>
                                        {% for categoria in categorias %}
                                        <option value="{{ categoria.id }}">{{ categoria.nome }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="descricao" class="form-label">Descrição</label>
                                <textarea class="form-control" id="descricao" name="descricao" rows="3"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="preco_custo" class="form-label">Preço de Custo *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">R$</span>
                                        <input type="number" class="form-control" id="preco_custo" name="preco_custo" 
                                               step="0.01" min="0" required>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="lucro_desejado" class="form-label">Lucro Desejado *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="lucro_desejado" name="lucro_desejado" 
                                               step="0.01" min="0" required>
                                        <select class="form-select" id="tipo_lucro" name="tipo_lucro" style="max-width: 100px;">
                                            <option value="percentual">%</option>
                                            <option value="valor">R$</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Preço de Venda</label>
                                    <div class="price-display">
                                        <div class="price-value" id="preco_venda_display">R$ 0,00</div>
                                        <small>Calculado automaticamente</small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="estoque_atual" class="form-label">Estoque Atual *</label>
                                    <input type="number" class="form-control" id="estoque_atual" name="estoque_atual" 
                                           min="0" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="estoque_minimo" class="form-label">Estoque Mínimo *</label>
                                    <input type="number" class="form-control" id="estoque_minimo" name="estoque_minimo" 
                                           min="0" required>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-primary" onclick="calcularPreco()">
                                    <i class="fas fa-calculator me-1"></i>Calcular Preço
                                </button>
                                <div>
                                    <a href="{{ url_for('produtos') }}" class="btn btn-secondary me-2">Cancelar</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Salvar Produto
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function calcularPreco() {
            const precoCusto = parseFloat(document.getElementById('preco_custo').value) || 0;
            const lucroDesejado = parseFloat(document.getElementById('lucro_desejado').value) || 0;
            const tipoLucro = document.getElementById('tipo_lucro').value;
            
            if (precoCusto <= 0) {
                alert('Por favor, informe o preço de custo');
                return;
            }
            
            fetch('/api/calcular-preco', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    preco_custo: precoCusto,
                    lucro_desejado: lucroDesejado,
                    tipo_lucro: tipoLucro
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.preco_venda) {
                    document.getElementById('preco_venda_display').textContent = 
                        'R$ ' + data.preco_venda.toFixed(2).replace('.', ',');
                } else {
                    alert('Erro ao calcular preço: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao calcular preço');
            });
        }
        
        // Calcular automaticamente quando os campos mudarem
        document.getElementById('preco_custo').addEventListener('input', calcularPreco);
        document.getElementById('lucro_desejado').addEventListener('input', calcularPreco);
        document.getElementById('tipo_lucro').addEventListener('change', calcularPreco);
    </script>
</body>
</html>
