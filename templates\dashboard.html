{% extends "base.html" %}

{% block title %}Dashboard - Saúde Flex{% endblock %}

{% block content %}
<div class="dashboard">
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-2 gradient-text">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </h1>
                <p class="text-muted mb-0">Bem-vindo de volta! Aqui está um resumo do seu negócio hoje.</p>
            </div>
            <div class="d-flex align-items-center gap-3">
                <div class="text-end">
                    <div class="text-muted small">
                        <i class="fas fa-calendar me-1"></i>
                        <span id="data-atual"></span>
                    </div>
                    <div class="text-muted small">
                        <i class="fas fa-clock me-1"></i>
                        <span id="hora-atual"></span>
                    </div>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-filter me-2"></i>
                        Período
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="filtrarPeriodo('hoje')">Hoje</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filtrarPeriodo('semana')">Esta Semana</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filtrarPeriodo('mes')">Este Mês</a></li>
                        <li><a class="dropdown-item" href="#" onclick="filtrarPeriodo('ano')">Este Ano</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Statistics Cards -->
<div class="row mb-5">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card hover-lift h-100">
            <div class="card-body d-flex align-items-center">
                <div class="stat-icon primary me-3">
                    <i class="fas fa-box"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number" id="stat-produtos">{{ estatisticas.total_produtos }}</div>
                    <div class="stat-label">Total de Produtos</div>
                    <div class="small text-success mt-1">
                        <i class="fas fa-arrow-up me-1"></i>
                        <span id="crescimento-produtos">+12%</span> este mês
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card hover-lift h-100">
            <div class="card-body d-flex align-items-center">
                <div class="stat-icon success me-3">
                    <i class="fas fa-users"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number" id="stat-clientes">{{ estatisticas.total_clientes }}</div>
                    <div class="stat-label">Total de Clientes</div>
                    <div class="small text-success mt-1">
                        <i class="fas fa-arrow-up me-1"></i>
                        <span id="crescimento-clientes">+8%</span> este mês
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card hover-lift h-100">
            <div class="card-body d-flex align-items-center">
                <div class="stat-icon info me-3">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number" id="stat-agendamentos">{{ estatisticas.agendamentos_hoje }}</div>
                    <div class="stat-label">Agendamentos Hoje</div>
                    <div class="small text-info mt-1">
                        <i class="fas fa-clock me-1"></i>
                        <span id="proximos-agendamentos">3 próximos</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card hover-lift h-100">
            <div class="card-body d-flex align-items-center">
                <div class="stat-icon warning me-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number" id="stat-estoque">{{ estatisticas.estoque_baixo }}</div>
                    <div class="stat-label">Estoque Baixo</div>
                    <div class="small text-warning mt-1">
                        <i class="fas fa-eye me-1"></i>
                        <a href="{{ url_for('relatorio_estoque_baixo') }}" class="text-warning text-decoration-none">Ver detalhes</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics Section -->
<div class="row mb-5">
    <div class="col-lg-8">
        <div class="card hover-lift">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2 text-primary"></i>
                    Vendas dos Últimos 7 Dias
                </h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-calendar me-1"></i>
                        7 dias
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="atualizarGrafico('7d')">7 dias</a></li>
                        <li><a class="dropdown-item" href="#" onclick="atualizarGrafico('30d')">30 dias</a></li>
                        <li><a class="dropdown-item" href="#" onclick="atualizarGrafico('90d')">90 dias</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <canvas id="vendasChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card hover-lift h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2 text-primary"></i>
                    Produtos Mais Vendidos
                </h5>
            </div>
            <div class="card-body">
                <canvas id="produtosChart" height="200"></canvas>
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small fw-bold">Óleo Essencial</span>
                        <span class="badge bg-primary">45%</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="small fw-bold">Creme Relaxante</span>
                        <span class="badge bg-success">30%</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="small fw-bold">Chá Detox</span>
                        <span class="badge bg-info">25%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Goals and Quick Actions -->
<div class="row mb-5">
    <div class="col-lg-8">
        <div class="card hover-lift">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-target me-2 text-primary"></i>
                    Metas do Mês
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Meta de Vendas</span>
                            <span class="text-muted">R$ 15.000 / R$ 25.000</span>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar" role="progressbar" style="width: 60%" id="progresso-vendas"></div>
                        </div>
                        <small class="text-success">
                            <i class="fas fa-arrow-up me-1"></i>
                            60% da meta atingida
                        </small>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Novos Clientes</span>
                            <span class="text-muted">12 / 20</span>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 60%" id="progresso-clientes"></div>
                        </div>
                        <small class="text-info">
                            <i class="fas fa-arrow-up me-1"></i>
                            60% da meta atingida
                        </small>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Agendamentos</span>
                            <span class="text-muted">45 / 60</span>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 75%" id="progresso-agendamentos"></div>
                        </div>
                        <small class="text-warning">
                            <i class="fas fa-arrow-up me-1"></i>
                            75% da meta atingida
                        </small>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-bold">Produtos Cadastrados</span>
                            <span class="text-muted">8 / 10</span>
                        </div>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 80%" id="progresso-produtos"></div>
                        </div>
                        <small class="text-primary">
                            <i class="fas fa-arrow-up me-1"></i>
                            80% da meta atingida
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card hover-lift h-100">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2 text-primary"></i>
                    Ações Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('venda_nova') }}" class="btn btn-success btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Nova Venda
                    </a>
                    <a href="{{ url_for('agendamento_novo') }}" class="btn btn-info btn-lg">
                        <i class="fas fa-calendar-plus me-2"></i>
                        Novo Agendamento
                    </a>
                    <a href="{{ url_for('cliente_novo') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>
                        Novo Cliente
                    </a>
                    <a href="{{ url_for('produto_novo') }}" class="btn btn-warning btn-lg">
                        <i class="fas fa-plus-circle me-2"></i>
                        Novo Produto
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alertas do Sistema -->
<div class="row mb-4" id="alertas-sistema">
    <!-- Alertas serão carregados via JavaScript -->
</div>

<!-- Últimos Agendamentos -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Últimos Agendamentos
                </h6>
                <a href="{{ url_for('agendamentos') }}" class="btn btn-sm btn-outline-primary">
                    Ver Todos
                </a>
            </div>
            <div class="card-body">
                {% if ultimos_agendamentos %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Cliente</th>
                                <th>Produto</th>
                                <th>Vendedor</th>
                                <th>Data/Hora</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agendamento in ultimos_agendamentos %}
                            <tr>
                                <td>
                                    <i class="fas fa-user me-2 text-muted"></i>
                                    {{ agendamento.cliente_nome }}
                                </td>
                                <td>
                                    <i class="fas fa-box me-2 text-muted"></i>
                                    {{ agendamento.produto_nome }}
                                </td>
                                <td>
                                    <i class="fas fa-user-tie me-2 text-muted"></i>
                                    {{ agendamento.vendedor_nome }}
                                </td>
                                <td>
                                    <i class="fas fa-clock me-2 text-muted"></i>
                                    {{ agendamento.data_agendamento }}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if agendamento.status == 'confirmado' else 'primary' if agendamento.status == 'agendado' else 'secondary' }}">
                                        {{ agendamento.status.title() }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <p class="text-muted">Nenhum agendamento encontrado.</p>
                    <a href="{{ url_for('agendamento_novo') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Criar Primeiro Agendamento
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Dashboard specific styles */
.dashboard-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.weather-widget {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    color: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.time-widget {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    color: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.chart-container {
    position: relative;
    height: 300px;
}

.metric-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition-normal);
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-800);
}

.metric-label {
    color: var(--gray-600);
    font-size: 0.875rem;
    font-weight: 500;
}

.metric-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.metric-change.positive {
    color: var(--success-color);
}

.metric-change.negative {
    color: var(--danger-color);
}

/* Animation for counters */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.counter {
    animation: countUp 0.8s ease-out;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/pt-br.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment"></script>
<script>
moment.locale('pt-br');

// Update date and time
function updateDateTime() {
    document.getElementById('data-atual').textContent = moment().format('DD/MM/YYYY');
    document.getElementById('hora-atual').textContent = moment().format('HH:mm:ss');
}

updateDateTime();
setInterval(updateDateTime, 1000);

// Chart.js Configuration
Chart.defaults.font.family = 'Inter, sans-serif';
Chart.defaults.color = '#6b7280';

// Sales Chart
const vendasCtx = document.getElementById('vendasChart').getContext('2d');
const vendasChart = new Chart(vendasCtx, {
    type: 'line',
    data: {
        labels: ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb', 'Dom'],
        datasets: [{
            label: 'Vendas (R$)',
            data: [1200, 1900, 800, 1500, 2000, 1800, 2200],
            borderColor: 'rgb(102, 126, 234)',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: 'rgb(102, 126, 234)',
            pointBorderColor: '#fff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)'
                },
                ticks: {
                    callback: function(value) {
                        return 'R$ ' + value.toLocaleString('pt-BR');
                    }
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        },
        elements: {
            point: {
                hoverBackgroundColor: 'rgb(102, 126, 234)'
            }
        }
    }
});

// Products Chart
const produtosCtx = document.getElementById('produtosChart').getContext('2d');
const produtosChart = new Chart(produtosCtx, {
    type: 'doughnut',
    data: {
        labels: ['Óleo Essencial', 'Creme Relaxante', 'Chá Detox'],
        datasets: [{
            data: [45, 30, 25],
            backgroundColor: [
                'rgb(102, 126, 234)',
                'rgb(16, 185, 129)',
                'rgb(59, 130, 246)'
            ],
            borderWidth: 0,
            hoverOffset: 4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        cutout: '70%'
    }
});

// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.stat-number');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        const increment = target / 50;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 30);
    });
}

// Filter functions
function filtrarPeriodo(periodo) {
    console.log('Filtrando por período:', periodo);
    // Implement period filtering logic here
    SaudeFlex.notify.info(`Filtrando dados por: ${periodo}`);
}

function atualizarGrafico(periodo) {
    console.log('Atualizando gráfico para:', periodo);
    // Implement chart update logic here
    SaudeFlex.notify.info(`Gráfico atualizado para: ${periodo}`);
}

// Sistema de Alertas
async function carregarAlertas() {
    const alertasContainer = document.getElementById('alertas-sistema');

    try {
        const response = await fetch('/api/alertas');
        const data = await response.json();
        const alertas = data.alertas;

        // Verificar se há alertas para mostrar
        if (alertas.length === 0) {
            alertasContainer.style.display = 'none';
            return;
        }

        let alertasHtml = '';
        alertas.forEach(alerta => {
            alertasHtml += `
                <div class="col-md-6 mb-3">
                    <div class="alert alert-${alerta.cor} alert-dismissible fade show" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-${alerta.icone} fa-2x"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="alert-heading mb-1">${alerta.titulo}</h6>
                                <p class="mb-2">${alerta.mensagem}</p>
                                <a href="${alerta.link}" class="btn btn-sm btn-outline-${alerta.cor}">
                                    ${alerta.acao}
                                </a>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>
            `;
        });

        alertasContainer.innerHTML = alertasHtml;
    } catch (error) {
        console.error('Erro ao carregar alertas:', error);
        alertasContainer.style.display = 'none';
    }
}

// Verificar notificações em tempo real
function verificarNotificacoes() {
    // Simular verificação de notificações
    const agora = new Date();
    const hora = agora.getHours();

    // Notificação de bom dia
    if (hora >= 8 && hora < 12) {
        const ultimaNotificacao = localStorage.getItem('ultima-notificacao-bomdia');
        const hoje = agora.toDateString();

        if (ultimaNotificacao !== hoje) {
            setTimeout(() => {
                SaudeFlex.notify.info('Bom dia! Tenha um ótimo dia de vendas! 🌅', {
                    duration: 8000
                });
                localStorage.setItem('ultima-notificacao-bomdia', hoje);
            }, 2000);
        }
    }

    // Verificar estoque baixo (simulado)
    setTimeout(() => {
        const estoquesBaixos = Math.floor(Math.random() * 3) + 1; // 1-3 produtos
        if (estoquesBaixos > 0) {
            SaudeFlex.notify.warning(`${estoquesBaixos} produto(s) com estoque baixo!`, {
                duration: 10000
            });
        }
    }, 5000);
}

// Atualizar estatísticas em tempo real
async function atualizarEstatisticas() {
    try {
        const response = await fetch('/api/dashboard-stats');
        const stats = await response.json();

        // Atualizar cards de estatísticas
        const cards = document.querySelectorAll('.card .h5.mb-0.font-weight-bold.text-gray-800');
        if (cards.length >= 4) {
            cards[0].textContent = stats.total_produtos;
            cards[1].textContent = stats.total_clientes;
            cards[2].textContent = stats.agendamentos_hoje;
            cards[3].textContent = stats.estoque_baixo;
        }

        // Mostrar notificação se houver mudanças críticas
        if (stats.estoque_baixo > 0) {
            // Verificar se já foi notificado hoje
            const ultimaNotificacao = localStorage.getItem('ultima-notificacao-estoque');
            const hoje = new Date().toDateString();

            if (ultimaNotificacao !== hoje) {
                SaudeFlex.notify.warning(`${stats.estoque_baixo} produto(s) com estoque baixo!`, {
                    duration: 10000
                });
                localStorage.setItem('ultima-notificacao-estoque', hoje);
            }
        }

        console.log('Estatísticas atualizadas:', stats);
    } catch (error) {
        console.error('Erro ao atualizar estatísticas:', error);
    }
}

// Inicialização
document.addEventListener('DOMContentLoaded', function() {
    // Animate counters on load
    setTimeout(animateCounters, 500);

    // Load alerts and notifications
    carregarAlertas();
    verificarNotificacoes();

    // Update statistics every 30 seconds
    setInterval(atualizarEstatisticas, 30000);

    // Check notifications every 5 minutes
    setInterval(verificarNotificacoes, 300000);

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Welcome notification
    setTimeout(() => {
        SaudeFlex.notify.success('Dashboard carregado com sucesso! 🎉', {
            duration: 3000
        });
    }, 1000);
});
</script>
{% endblock %}
</div> <!-- Fim da div dashboard -->
